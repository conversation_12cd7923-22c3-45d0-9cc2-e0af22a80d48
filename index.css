/* Index Page Specific Styles */

/* Hero Section */
.hero-section {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    background: linear-gradient(135deg, #e6f9fa 0%, #f0feff 100%);
    border-bottom-left-radius: 40px;
    border-bottom-right-radius: 40px;
    min-height: 85vh;
    display: flex;
    align-items: center;
    margin-top: 70px;
}

/* <PERSON> Shapes - 3D Elements */
.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    animation: floatAnimation 8s infinite ease-in-out;
}

.shape-1 {
    top: 20%;
    left: 10%;
    width: 80px;
    height: 80px;
    animation-delay: 0s;
}

.shape-2 {
    top: 60%;
    left: 15%;
    width: 120px;
    height: 120px;
    animation-delay: 2s;
    background: linear-gradient(45deg, rgba(38, 224, 127, 0.05) 0%, rgba(173, 252, 234, 0.05) 100%);
}

.shape-3 {
    top: 25%;
    right: 10%;
    width: 150px;
    height: 150px;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    background: linear-gradient(45deg, rgba(19, 197, 193, 0.05) 0%, rgba(38, 224, 127, 0.05) 100%);
    animation-delay: 3s;
    animation-duration: 12s;
}

.shape-4 {
    bottom: 15%;
    right: 20%;
    width: 100px;
    height: 100px;
    animation-delay: 1s;
    background: linear-gradient(45deg, rgba(173, 252, 234, 0.08) 0%, rgba(19, 197, 193, 0.08) 100%);
}

@keyframes floatAnimation {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
    100% { transform: translateY(0) rotate(0deg); }
}

/* Hero Content Styles */
.hero-row {
    position: relative;
    z-index: 2;
}

.hero-content-wrapper {
    display: flex;
    align-items: center;
    padding-right: 20px;
}

.hero-content {
    max-width: 580px;
}

.hero-badge {
    display: inline-block;
    background: rgba(38, 224, 127, 0.1);
    color: var(--primary);
    font-size: 1rem;
    font-weight: 600;
    padding: 10px 15px;
    border-radius: 30px;
    margin-bottom: 20px;
    position: relative;
}

.hero-badge::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: var(--primary);
    border-radius: 50%;
    margin-left: 8px;
}

.hero-title {
    font-size: 3.2rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 20px;
    color: #222;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
    color: #666;
    line-height: 1.8;
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 40px;
}

.btn-hero-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: var(--primary-gradient);
    color: white;
    font-weight: 700;
    padding: 14px 30px;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-hero-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(38, 224, 127, 0.3);
    color: white;
}

.btn-hero-primary::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    transform: rotate(30deg);
    transition: all 1.5s cubic-bezier(0.19, 1, 0.22, 1);
    opacity: 0;
}

.btn-hero-primary:hover::after {
    opacity: 1;
    left: 100%;
}

.btn-hero-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: #333;
    font-weight: 600;
    padding: 14px 30px;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(38, 224, 127, 0.2);
}

.btn-hero-secondary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
    color: var(--primary);
    border-color: var(--primary);
}

/* Hero Stats */
.hero-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 20px;
    justify-content: center;
}

.hero-stat-item {
    position: relative;
    text-align: center;
}

.hero-stat-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 50%;
    left: -15px;
    transform: translateY(-50%);
    width: 1px;
    height: 70%;
    background: linear-gradient(to bottom, transparent, rgba(38, 224, 127, 0.3), transparent);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary);
    line-height: 1;
    margin-bottom: 8px;
}

.counter {
    display: inline-block;
}

.stat-label {
    font-size: 0.95rem;
    color: #888;
    font-weight: 500;
}

/* Hero Image */
.hero-image-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    overflow: visible;
}

.hero-image-container {
    position: relative;
    width: 100%;
    max-width: 550px;
    overflow: visible;
}

.hero-image {
    position: relative;
    z-index: 2;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
}

.hero-image:hover {
    transform: translateY(-10px);
    box-shadow: 0 35px 60px rgba(38, 224, 127, 0.15);
}

.main-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    vertical-align: middle;
    transition: transform 0.7s ease;
}

.hero-image:hover .main-image {
    transform: scale(1.03);
}

.image-blob {
    position: absolute;
    top: -20px;
    right: -40px;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    opacity: 0.1;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    z-index: 1;
    animation: blobAnimation 10s infinite alternate ease-in-out;
}

@keyframes blobAnimation {
    0% {
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    50% {
        border-radius: 50% 50% 30% 70% / 40% 60% 40% 60%;
    }
    100% {
        border-radius: 70% 30% 50% 50% / 60% 40% 60% 40%;
    }
}

/* Floating badges */
.floating-badge {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    z-index: 3;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    min-width: 150px;
}

.badge-tech {
    top: 20px;
    right: 10px;
    animation: floatBadge 6s infinite ease-in-out;
}

.badge-quality {
    bottom: 30px;
    left: 10px;
    animation: floatBadge 6s infinite ease-in-out 2.5s;
}

@keyframes floatBadge {
    0% { transform: translateY(0); }
    50% { transform: translateY(-12px); }
    100% { transform: translateY(0); }
}

.badge-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: rgba(38, 224, 127, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.floating-badge:hover .badge-icon {
    background: var(--primary-gradient);
    transform: scale(1.05);
}

.badge-icon img {
    width: 28px;
    height: 28px;
    transition: all 0.3s ease;
}

.floating-badge:hover .badge-icon img {
    filter: brightness(0) invert(1);
    transform: rotate(15deg);
}

/* For FontAwesome icons in badges */
.badge-icon i {
    color: var(--primary);
    font-size: 30px;
    transition: all 0.3s ease;
}

.floating-badge:hover .badge-icon i {
    color: white !important;
    transform: rotate(15deg);
}

.floating-badge span {
    font-weight: 700;
    font-size: 1rem;
    color: #333;
    margin-bottom: 8px;
    white-space: nowrap;
}

.stars-rating {
    display: flex;
    gap: 3px;
    margin-top: 2px;
}

.stars-rating i {
    color: #FFD700;
    font-size: 14px;
}

/* Responsive Styles */
@media (max-width: 991px) {
    .hero-section {
        padding: 80px 0 60px;
        min-height: auto;
    }
    
    .hero-content-wrapper {
        padding-right: 0;
        margin-bottom: 30px;
        order: 1;
    }
    
    .hero-content {
        max-width: 100%;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .hero-badge::before {
        display: none;
    }
    
    .floating-badge {
        position: relative;
        top: auto;
        right: auto;
        bottom: auto;
        left: auto;
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        margin: 15px 10px;
        width: auto;
        min-width: 0;
    }

    .badge-icon {
        margin-bottom: 0;
        margin-left: 12px;
    }
    
    .floating-badge span {
        margin-bottom: 0;
        margin-right: 5px;
    }
    
    .stars-rating {
        margin-right: 8px;
    }
    
    .image-blob {
        display: none;
    }

    .hero-image-wrapper {
        order: 2;
        margin-top: 20px;
    }
    
    .container {
        max-width: 100%;
        padding-left: 15px;
        padding-right: 15px;
    }
}

@media (max-width: 767px) {
    .hero-section {
        padding: 60px 0 40px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-description {
        font-size: 1rem;
        margin-bottom: 20px;
    }
    
    .btn-hero-primary, .btn-hero-secondary {
        padding: 12px 25px;
    }
    
    .hero-stats {
        flex-wrap: wrap;
        gap: 10px;
        width: 100%;
        justify-content: space-around;
        margin-top: 15px;
    }
    
    .hero-stat-item {
        flex: 0 0 40%;
        margin-bottom: 15px;
        text-align: center;
    }
    
    .hero-stat-item:not(:last-child)::after {
        display: none;
    }
    
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .stat-number {
        font-size: 2.2rem;
    }
    
    .hero-image-container {
        max-width: 90%;
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: 40px 0 30px;
    }
    
    .hero-content-wrapper {
        margin-bottom: 20px;
    }
    
    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }
    
    .hero-description {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }
    
    .hero-stats {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        gap: 5px;
        margin-top: 10px;
    }
    
    .hero-stat-item {
        flex: 0 0 45%;
        text-align: center;
        margin-bottom: 15px;
        padding: 5px;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .btn-hero-primary, .btn-hero-secondary {
        width: 100%;
        margin-bottom: 10px;
        justify-content: center;
        padding: 10px 20px;
        font-size: 0.95rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        width: 100%;
        margin-bottom: 20px;
    }
    
    .container {
        padding-left: 5px;
        padding-right: 5px;
        width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .hero-image-container {
        max-width: 100%;
    }
    
    .floating-badge {
        padding: 10px;
        margin: 10px 5px;
        transform: scale(0.9);
    }
}

/* Feature Boxes */
.feature-box {
    background: #fff;
    border-radius: var(--border-radius);
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    padding: 2rem;
    height: 100%;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(230, 230, 230, 0.5);
}

.feature-box::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(to bottom, var(--primary-light) 0%, rgba(255,255,255,0) 100%);
    z-index: -1;
    transition: height 0.5s ease;
    opacity: 0.5;
}

.feature-box:hover::after {
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-color);
}

.feature-box img {
    width: 64px;
    height: 64px;
    transition: var(--transition);
    filter: drop-shadow(0 5px 15px rgba(38, 224, 127, 0.2));
}

.feature-box i {
    color: var(--primary);
    font-size: 3rem;
    transition: var(--transition);
}

/* Emergency Section */
.emergency-section {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem 0;
    margin: 2rem 0;
}

/* Testimonials */
.testimonial-box {
    background: #fff;
    border-radius: var(--border-radius);
    padding: 2rem;
    height: 100%;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(230, 230, 230, 0.5);
    transition: var(--transition);
}

.testimonial-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-color);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #eafafc 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    
  
    text-align: center;
}

/* Modern Products Section 2025 */
.products-section {
    position: relative;
    padding: 100px 0;
    background: linear-gradient(135deg, #f0f9ff 0%, #f9f9f9 100%);
    overflow: hidden;
}

.products-wrapper {
    position: relative;
    z-index: 2;
}

.blob-shape {
    position: absolute;
    top: -150px;
    left: -150px;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.07) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 12s infinite linear;
    opacity: 1;
    z-index: 1;
}

.blob-shape-2 {
    position: absolute;
    bottom: -150px;
    right: -150px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 10s infinite linear reverse;
    opacity: 1;
    z-index: 1;
}

.product-card-modern {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 24px;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
}

.product-card-modern:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(38, 224, 127, 0.15);
    border-color: rgba(38, 224, 127, 0.3);
}

.product-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.2) 0%, 
                rgba(255, 255, 255, 0) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 1;
}

.product-card-modern:hover::before {
    opacity: 1;
}

.product-icon-wrapper {
    position: relative;
    padding: 40px 0 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-icon-bg {
    position: absolute;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    border-radius: 50%;
    z-index: 1;
    transition: all 0.5s ease;
    box-shadow: 0 10px 30px rgba(38, 224, 127, 0.1);
}

.product-card-modern:hover .product-icon-bg {
    transform: scale(1.15);
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.15) 0%, rgba(19, 197, 193, 0.15) 100%);
    box-shadow: 0 15px 40px rgba(38, 224, 127, 0.2);
}

.product-icon {
    width: 80px;
    height: 80px;
    position: relative;
    z-index: 2;
    transition: all 0.5s ease;
    filter: drop-shadow(0 10px 15px rgba(38, 224, 127, 0.2));
}

.product-card-modern:hover .product-icon {
    transform: scale(1.1) translateY(-5px);
    filter: drop-shadow(0 15px 25px rgba(38, 224, 127, 0.3));
}

.product-content {
    padding: 20px 30px 40px;
    position: relative;
    z-index: 2;
}

.product-content h3 {
    margin-bottom: 20px;
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: -0.5px;
    text-align: center;
}

.feature-list-modern {
    list-style: none;
    padding: 0;
    margin: 0 0 30px;
    text-align: right;
}

.feature-list-modern li {
    position: relative;
    padding: 8px 25px 8px 0;
    transition: all 0.3s ease;
    color: #666;
    font-weight: 500;
}

.feature-list-modern li:hover {
    color: var(--primary);
    transform: translateX(-5px);
}

.feature-bullet {
    position: absolute;
    right: 0;
    top: 15px;
    width: 10px;
    height: 10px;
    background: linear-gradient(135deg, var(--primary) 0%, #13c5c1 100%);
    border-radius: 50%;
    display: inline-block;
}

.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    border: 1px solid rgba(38, 224, 127, 0.2);
    padding: 12px 25px;
    border-radius: 50px;
    color: var(--primary);
    font-weight: 700;
    transition: all 0.4s ease;
    text-decoration: none;
    margin-top: auto;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, var(--primary) 0%, #13c5c1 100%);
    transition: width 0.4s ease;
    z-index: -1;
}

.btn-modern:hover {
    color: white;
    border-color: transparent;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.25);
}

.btn-modern:hover::before {
    width: 100%;
}

.btn-modern i {
    transition: transform 0.3s ease;
}

.btn-modern:hover i {
    transform: translateX(-5px);
}

/* Add animation classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate__fadeIn {
    animation: fadeInUp 0.7s ease-out forwards;
}

/* About Section 2025 Styles */
.about-section {
    position: relative;
    background: linear-gradient(135deg, #f7fcff 0%, #fdfdfd 100%);
    padding: 100px 0;
    overflow: hidden;
    border-radius: 0 0 30px 30px;
}

.about-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px 0;
}

.about-blob-1 {
    position: absolute;
    top: -100px;
    left: -150px;
    width: 450px;
    height: 450px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 15s infinite linear;
    z-index: -1;
}

.about-blob-2 {
    position: absolute;
    bottom: -150px;
    right: -100px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 12s infinite linear reverse;
    z-index: -1;
}

.about-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiLz48L2c+PC9zdmc+');
    z-index: -1;
    opacity: 0.5;
}

/* Section Title with Separator */
.section-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
}

.title-separator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 40px;
    width: 80%;
    max-width: 300px;
}

.separator-line {
    height: 2px;
    background: linear-gradient(to right, rgba(38, 224, 127, 0), rgba(38, 224, 127, 0.5));
    flex-grow: 1;
}

.separator-line:last-child {
    background: linear-gradient(to left, rgba(38, 224, 127, 0), rgba(38, 224, 127, 0.5));
}

.separator-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 15px;
    box-shadow: 0 10px 20px rgba(38, 224, 127, 0.2);
}

.separator-icon i {
    color: white;
    font-size: 1.2rem;
}

/* About Content Styles */
.about-subtitle {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    padding-right: 15px;
}

.about-subtitle::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 4px;
    background: var(--primary-gradient);
    border-radius: 4px;
}

.about-description {
    font-size: 1.05rem;
    line-height: 1.8;
    margin-bottom: 30px;
    color: #555;
}

/* About Image Styles */
.about-image-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.about-image-wrapper {
    position: relative;
    z-index: 1;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.about-image {
    width: 100%;
    border-radius: 20px;
    transition: transform 0.7s ease;
    vertical-align: middle;
}

.about-image-wrapper:hover .about-image {
    transform: scale(1.05);
}

.about-image-shape {
    position: absolute;
    top: -20px;
    right: -40px;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    opacity: 0.1;
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    z-index: -1;
    animation: blobAnimation 15s infinite alternate ease-in-out;
}

/* Floating Image Cards */
.image-card-1, .image-card-2 {
    position: absolute;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    z-index: 3;
    transition: all 0.5s ease;
    border: 1px solid rgba(38, 224, 127, 0.1);
    max-width: 200px;
}

.image-card-1 {
    top: 30px;
    left: -50px;
    animation: floatCard 6s infinite ease-in-out;
}

.image-card-2 {
    bottom: 40px;
    right: -50px;
    animation: floatCard 6s infinite ease-in-out 3s;
}

@keyframes floatCard {
    0% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0); }
}

.image-card-1:hover, .image-card-2:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(38, 224, 127, 0.15);
    border-color: rgba(38, 224, 127, 0.3);
}

.card-icon {
    width: 40px;
    height: 40px;
    min-width: 40px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    transition: all 0.3s ease;
}

.image-card-1:hover .card-icon, .image-card-2:hover .card-icon {
    background: var(--primary-gradient);
}

.card-icon i {
    font-size: 1.2rem;
    color: var(--primary);
    transition: all 0.3s ease;
}

.image-card-1:hover .card-icon i, .image-card-2:hover .card-icon i {
    color: white;
}

.card-content h5 {
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #333;
}

.card-content p {
    font-size: 0.75rem;
    margin-bottom: 0;
    color: #666;
}

/* Experience Badge */
.experience-badge {
    position: absolute;
    bottom: 60px;
    left: -20px;
    width: 120px;
    height: 120px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 15px 35px rgba(38, 224, 127, 0.3);
    animation: pulse 3s infinite;
    z-index: 2;
}

.badge-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: white;
}

.badge-number {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
}

.badge-text {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 18px;
    padding: 20px;
    text-align: center;
    transition: all 0.4s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(38, 224, 127, 0.1);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    background: linear-gradient(to bottom, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 100%);
    z-index: -1;
    transition: height 0.5s ease;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(38, 224, 127, 0.15);
    border-color: rgba(38, 224, 127, 0.3);
}

.stat-card:hover::before {
    height: 100%;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    background: var(--primary-gradient);
    transform: scale(1.1) rotateY(180deg);
}

.stat-icon i {
    font-size: 1.2rem;
    color: var(--primary);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon i {
    color: white;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary);
    margin-bottom: 5px;
    line-height: 1;
}

.stat-title {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
}

/* CTA Button */
.about-cta {
    margin-top: 40px;
}

.btn-about-more {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    color: var(--primary);
    font-weight: 700;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(38, 224, 127, 0.3);
}

.btn-about-more::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.4s ease;
    z-index: -1;
}

.btn-about-more:hover {
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(38, 224, 127, 0.2);
}

.btn-about-more:hover::before {
    width: 100%;
}

.btn-about-more i {
    transition: transform 0.3s ease;
}

.btn-about-more:hover i {
    transform: translateX(-8px);
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .about-section {
        padding: 80px 0;
    }
    
    .section-title {
        font-size: 2.2rem;
    }
    
    .experience-badge {
        width: 100px;
        height: 100px;
        bottom: 40px;
        left: -10px;
    }
    
    .badge-number {
        font-size: 2rem;
    }
    
    .badge-text {
        font-size: 0.7rem;
    }
    
    .image-card-1, .image-card-2 {
        max-width: 180px;
        padding: 12px;
    }
    
    .image-card-1 {
        left: -30px;
    }
    
    .image-card-2 {
        right: -30px;
    }
    
    .about-content {
        margin-top: 40px;
    }
    
    .row {
        margin-left: 0;
        margin-right: 0;
    }
}

@media (max-width: 767px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        width: 100%;
    }
    
    .stat-card {
        max-width: 100%;
        
    }
    
    .image-card-1, .image-card-2 {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
        margin: -30px auto 20px;
        z-index: 4;
        max-width: 80%;
    }
    
    .experience-badge {
        bottom: 20px;
        left: 10px;
    }
    
    .about-image-wrapper {
        max-width: 80%;
        margin: 0 auto;
    }
    
    .col-md-3 {
        width: 50%;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        width: 100%;
    }
    
  
    .col-md-3 {
        width: 100%;
    }
}

/* Order Section 2025 Styles */
.order-section {
    position: relative;
    background: linear-gradient(135deg, #f8fbff 0%, #fafeff 100%);
    padding: 100px 0;
    overflow: hidden;
    margin: 50px 0;
}

.order-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px 0;
}

.order-blob {
    position: absolute;
    bottom: -200px;
    right: -200px;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 15s infinite linear;
    z-index: -1;
}

.order-shape {
    position: absolute;
    top: -150px;
    left: -150px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: blob-animation 12s infinite linear reverse;
    z-index: -1;
}

.order-dots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiLz48L2c+PC9zdmc+');
    z-index: -1;
    opacity: 0.5;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

/* Order Card Styles */
.order-card {
    position: relative;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.07);
    transition: all 0.5s ease;
    border: 1px solid rgba(255, 255, 255, 0.8);
    padding: 40px;
    margin-bottom: 50px;
    z-index: 2;
}

.order-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 35px 60px rgba(38, 224, 127, 0.1);
}

.order-card-shape {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.03) 0%, rgba(19, 197, 193, 0.03) 100%);
    border-radius: 0 24px 0 300px;
    z-index: -1;
}

.order-form-container {
    position: relative;
    z-index: 2;
}

.order-form {
    position: relative;
}

/* Custom Form Controls */
.form-group {
    position: relative;
    margin-bottom: 20px;
}

.floating-label {
    position: relative;
}

.custom-input {
    height: 60px;
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(38, 224, 127, 0.2);
    border-radius: 15px;
    padding: 10px 20px 10px 50px;
    font-size: 1rem;
    transition: all 0.3s ease;
    color: #333;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.02);
}

.custom-input:focus {
    background: #fff;
    border-color: var(--primary);
    box-shadow: 0 5px 25px rgba(38, 224, 127, 0.15);
    transform: translateY(-2px);
}

.form-label {
    position: absolute;
    top: 20px;
    right: 50px;
    color: #666;
    transition: all 0.3s ease;
    pointer-events: none;
    font-weight: 500;
}

.custom-input:focus ~ .form-label,
.custom-input:not(:placeholder-shown) ~ .form-label {
    top: -10px;
    right: 20px;
    font-size: 0.8rem;
    padding: 0 5px;
    background: white;
    border-radius: 5px;
    color: var(--primary);
    font-weight: 600;
}

.input-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    text-align: center;
    color: #999;
    transition: all 0.3s ease;
}

.custom-input:focus ~ .input-icon {
    color: var(--primary);
}

.custom-textarea {
    height: 120px;
    padding-top: 20px;
    resize: none;
}

.custom-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23999' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: left 20px center;
    background-size: 12px;
}

.custom-select:focus {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%2326e07f' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
}

/* Submit Button */
.btn-order-submit {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    padding: 0 30px;
    height: 60px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.25);
    min-width: 220px;
}

.btn-order-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0.7s ease;
}

.btn-order-submit:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(38, 224, 127, 0.35);
}

.btn-order-submit:hover::before {
    transform: translateX(100%);
}

.btn-order-submit .btn-text {
    margin-right: 15px;
}

.btn-order-submit .btn-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-order-submit:hover .btn-icon {
    transform: rotate(45deg);
}

/* Promo Badge */
.promo-badge {
    position: absolute;
    top: 30px;
    left: 30px;
    width: 90px;
    height: 90px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.3);
    animation: pulse 3s infinite;
    z-index: 3;
    transform: rotate(-15deg);
}

.badge-inner {
    text-align: center;
    color: white;
}

.discount {
    font-size: 1.8rem;
    font-weight: 800;
    line-height: 1;
}

.percent {
    font-size: 1rem;
    font-weight: 600;
}

.badge-text {
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 1.2;
}

/* Order Features */
.order-features {
    display: flex;
    justify-content: space-around;
    margin-top: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px 20px;
    transition: all 0.3s ease;
    border: 1px solid rgba(38, 224, 127, 0.1);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.03);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(38, 224, 127, 0.1);
    border-color: rgba(38, 224, 127, 0.2);
}

.feature-icon {
    width: 40px;
    height: 40px;
    min-width: 40px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
    background: var(--primary-gradient);
}

.feature-icon i {
    font-size: 1.2rem;
    color: var(--primary);
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon i {
    color: white;
}

.feature-text {
    font-weight: 600;
    color: #444;
    font-size: 0.95rem;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .order-section {
        padding: 80px 0;
    }
    
    .order-card {
        padding: 30px;
    }
    
    .promo-badge {
        width: 80px;
        height: 80px;
        top: 20px;
        left: 20px;
    }
    
    .discount {
        font-size: 1.5rem;
    }
    
    .feature-item {
        flex: 0 0 calc(50% - 20px);
    }
}

@media (max-width: 767px) {
    .order-card {
        padding: 20px;
    }
    
    .custom-input {
        height: 55px;
    }
    
    .btn-order-submit {
        height: 55px;
        font-size: 1rem;
        min-width: 200px;
    }
    
    .feature-item {
        flex: 0 0 100%;
    }
    
    .promo-badge {
        width: 70px;
        height: 70px;
        top: 15px;
        left: 15px;
    }
    
    .discount {
        font-size: 1.3rem;
    }
    
    .badge-text {
        font-size: 0.6rem;
    }
    
    .row {
        margin-left: -5px;
        margin-right: -5px;
    }
    
    .col-md-6, .col-lg-10, .col-12 {
        padding-left: 5px;
        padding-right: 5px;
    }
} 