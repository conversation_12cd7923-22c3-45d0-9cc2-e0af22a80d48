/* Contact Page Styles - 2025 Design System */

/* Base container fix for mobile */
body, html {
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
}

.container {
  width: 100%;
  padding-right: var(--bs-gutter-x, 1rem);
  padding-left: var(--bs-gutter-x, 1rem);
  max-width: 100%;
}

/* Hero Section */
.contact-hero-2025 {
  position: relative;
  min-height: 650px;
  padding: 140px 0 80px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8feff 0%, #e6fbf2 100%);
  z-index: 1;
  width: 100%;;
  max-width: 100%;
  margin-top: 30px;
}
.hero-blob-1 {
  position: absolute;
  top: -150px;
  right: -100px;
  width: 550px;
  height: 550px;
  background: radial-gradient(circle, rgba(38, 224, 127, 0.08) 0%, rgba(255,255,255,0) 70%);
  border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
  animation: blob-float 15s infinite alternate ease-in-out;
  z-index: -1;
}

.hero-blob-2 {
  position: absolute;
  bottom: -200px;
  left: -150px;
  width: 450px;
  height: 450px;
  background: radial-gradient(circle, rgba(19, 197, 193, 0.08) 0%, rgba(255,255,255,0) 70%);
  border-radius: 58% 42% 35% 65% / 33% 45% 55% 67%;
  animation: blob-float 12s infinite alternate-reverse ease-in-out;
  z-index: -1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2326e07f' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: -1;
}

@keyframes blob-float {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  50% {
    transform: translate(20px, -30px) rotate(5deg) scale(1.05);
  }
  100% {
    transform: translate(-20px, 20px) rotate(-5deg) scale(0.95);
  }
}

.contact-hero-content {
  position: relative;
  z-index: 2;
}

.contact-badge {
  display: inline-block;
  background: linear-gradient(135deg, rgba(38, 224, 127, 0.15) 0%, rgba(19, 197, 193, 0.15) 100%);
  color: var(--primary);
  font-weight: 700;
  padding: 10px 20px;
  border-radius: 30px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.contact-badge:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
  transform: translateX(-100%);
  animation: badge-shine 3s infinite;
}

@keyframes badge-shine {
  100% {
    transform: translateX(100%);
  }
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 30px;
  line-height: 1.2;
}

.hero-description {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 30px;
  max-width: 90%;
}

.contact-methods {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.contact-method-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 15px 20px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex: 1;
  min-width: 220px;
}

.contact-method-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(38, 224, 127, 0.15);
  border-color: rgba(38, 224, 127, 0.3);
}

.method-icon {
  width: 45px;
  height: 45px;
  background: var(--primary-gradient);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.method-content h6 {
  margin: 0 0 5px 0;
  font-weight: 700;
  color: #333;
}

.method-content p {
  margin: 0;
  color: #555;
}

.btn-hero-primary {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: var(--primary-gradient);
  color: white !important;
  text-decoration: none;
  padding: 16px 32px;
  border-radius: 50px;
  font-weight: 700;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 15px 30px rgba(38, 224, 127, 0.2);
  overflow: hidden;
  z-index: 1;
}

.btn-shine {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transform: rotate(30deg);
  animation: btn-shine 4s infinite;
  z-index: -1;
}

@keyframes btn-shine {
  0% {
    left: -100%;
  }
  20%, 100% {
    left: 100%;
  }
}

.btn-hero-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(38, 224, 127, 0.3);
  color: white;
}

.btn-hero-primary:hover .btn-icon {
  transform: translateX(5px);
}

/* Contact Visual */
.contact-visual {
  position: relative;
  z-index: 2;
  height: 100%;
}

.visual-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-circle {
  width: 320px;
  height: 320px;
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 30px 60px rgba(0,0,0,0.1);
  border: 15px solid rgba(255,255,255,0.5);
  overflow: hidden;
  animation: pulse-gentle 4s infinite alternate ease-in-out;
}

.main-circle::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(135deg, var(--primary) 0%, #13c5c1 100%);
  border-radius: 50%;
  opacity: 0.1;
  z-index: -1;
  filter: blur(20px);
}

.main-image {
  width: 180px;
  height: 180px;
  object-fit: contain;
  animation: float-image 5s infinite alternate ease-in-out;
}

@keyframes float-image {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  100% {
    transform: translateY(-10px) rotate(5deg);
  }
}

@keyframes pulse-gentle {
  0% {
    transform: scale(1);
    box-shadow: 0 30px 60px rgba(0,0,0,0.1);
  }
  100% {
    transform: scale(1.05);
    box-shadow: 0 40px 80px rgba(38, 224, 127, 0.2);
  }
}

.contact-icons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.contact-icons .icon-item {
  position: absolute;
  width: 60px;
  height: 60px;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.4rem;
  box-shadow: 0 15px 30px rgba(38, 224, 127, 0.2);
  border: 5px solid white;
}

.contact-icons .icon-item.phone {
  top: 20%;
  left: 0;
  animation: float-icon 3s infinite alternate ease-in-out;
}

.contact-icons .icon-item.email {
  top: 15%;
  right: 5%;
  animation: float-icon 4s infinite alternate-reverse ease-in-out;
}

.contact-icons .icon-item.chat {
  bottom: 10%;
  right: 10%;
  animation: float-icon 3.5s infinite alternate ease-in-out;
}

.contact-icons .icon-item.location {
  bottom: 20%;
  left: 5%;
  animation: float-icon 4.5s infinite alternate-reverse ease-in-out;
}

@keyframes float-icon {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}

/* Contact Channels */
.contact-channels {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.channel-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: #555;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  min-width: 120px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.channel-item i {
  font-size: 1.8rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  color: white;
}

.channel-item span {
  font-weight: 600;
  font-size: 0.95rem;
}

.channel-item.whatsapp i {
  background: linear-gradient(135deg, #25D366, #128C7E);
}

.channel-item.email i {
  background: linear-gradient(135deg, #4285F4, #34A853);
}

.channel-item.chat i {
  background: linear-gradient(135deg, #FF5722, #FF9800);
}

.channel-item.location i {
  background: linear-gradient(135deg, #9C27B0, #673AB7);
}

.channel-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  color: #333;
}

.channel-item:hover i {
  transform: scale(1.1);
}

/* Contact Info Section */
.contact-info-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid rgba(255,255,255,0.8);
  position: relative;
  overflow: hidden;
}

.contact-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--primary-gradient);
}

.contact-info-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(38, 224, 127, 0.15);
}

.contact-info-card .contact-icon {
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.contact-info-card:hover .contact-icon {
  transform: scale(1.1) rotate(10deg);
}

/* Form Styles */
.form-container {
  background: white;
  border-radius: 30px;
  padding: 40px;
  box-shadow: 0 30px 60px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
  z-index: 1;
  border: 1px solid rgba(255,255,255,0.8);
}

.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2326e07f' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/svg%3E");
  z-index: -1;
}

.form-floating {
  margin-bottom: 5px;
}

.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
  border-radius: 15px;
  border: 1px solid rgba(0,0,0,0.08);
  background: #f9f9f9;
  transition: all 0.3s ease;
}

.form-floating > .form-control:focus,
.form-floating > .form-select:focus {
  box-shadow: 0 0 0 4px rgba(38, 224, 127, 0.1);
  border-color: var(--primary);
  background: white;
}

.form-floating > textarea.form-control {
  height: calc(7rem + 2px);
}

.form-floating > label {
  padding: 1rem 1.25rem;
}

/* Working Hours Section */
.working-hours {
  background: linear-gradient(135deg, #f8feff 0%, #e6fbf2 100%);
  border-radius: 30px;
  position: relative;
  overflow: hidden;
  padding: 50px 30px;
  box-shadow: 0 30px 60px rgba(0,0,0,0.05);
  border: 1px solid rgba(255,255,255,0.8);
}

/* Social Media Section */
.social-icon {
  transition: all 0.3s ease;
  width: 60px;
  height: 60px;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.social-icon:hover {
  transform: translateY(-10px);
  background: var(--primary-gradient) !important;
  border-color: transparent !important;
  color: white !important;
  box-shadow: 0 20px 40px rgba(38, 224, 127, 0.2);
}

/* FAQ Section */
.accordion-item {
  margin-bottom: 15px;
  border: none;
  border-radius: 20px !important;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.03);
}

.accordion-button {
  border-radius: 20px !important;
  padding: 20px 25px;
  font-weight: 600;
  font-size: 1.1rem;
}

.accordion-button:not(.collapsed) {
  background: linear-gradient(135deg, rgba(38, 224, 127, 0.08) 0%, rgba(255,255,255,0) 100%);
  color: var(--primary);
}

.accordion-body {
  padding: 25px;
}

/* Newsletter Section */
.glass-effect {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: 30px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 30px 60px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
}

.glass-effect::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 70%);
  z-index: -1;
}

/* Newsletter input group styles */
.glass-effect .input-group {
  background: white;
  border-radius: 50px;
  padding: 5px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(235, 235, 235, 0.5);
}

.glass-effect .input-group .form-control {
  border: none;
  background: transparent;
  height: 50px;
  padding-left: 20px;
  box-shadow: none;
  border-radius: 50px;
  font-size: 1rem;
}

.glass-effect .input-group .btn {
  border-radius: 50px !important;
  background: var(--primary-gradient) !important;
  border: none !important;
  color: white !important;
  padding: 10px 20px !important;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;
  min-width: 120px;
}

.glass-effect .input-group .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: -1;
}

.glass-effect .input-group .btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(38, 224, 127, 0.2);
}

.glass-effect .input-group .btn:hover::before {
  transform: translateX(100%);
}

/* Footer Newsletter Form Fix */
.footer-content .newsletter-form .form-container {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 5px;
  display: flex;
  overflow: hidden;
  border: none;
  box-shadow: none;
}

.footer-content .form-container input {
  border: none;
  background: transparent;
  padding: 15px 20px;
  color: white;
  flex: 1;
  outline: none;
  font-size: 0.95rem;
  height: auto;
  width: auto;
}

.footer-content .form-container input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.footer-content .form-container button {
  background: var(--primary-gradient);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 12px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  min-width: 50px;
  position: relative;
  overflow: hidden;
}

.footer-content .form-container button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(45deg);
  transition: all 0.6s ease;
}

.footer-content .form-container button:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(38, 224, 127, 0.3);
}

.footer-content .form-container button:hover::before {
  left: 100%;
  top: 100%;
}

.footer-content .form-container button i {
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.footer-content .form-container button:hover i {
  transform: translateX(3px);
}

/* Contact form button fix */
.contact-form .btn-primary {
  background: var(--primary-gradient) !important;
  border: none !important;
  padding: 15px 35px !important;
  font-weight: 700 !important;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  transition: all 0.3s ease;
  box-shadow: 0 15px 30px rgba(38, 224, 127, 0.2);
  border-radius: 50px !important;
  color: white !important;
}

.contact-form .btn-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(38, 224, 127, 0.3);
}

.contact-form .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: -1;
}

.contact-form .btn-primary:hover::before {
  transform: translateX(100%);
}

/* Submission Button */
.submit-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: var(--primary-gradient) !important;
  color: white !important;
  border: none !important;
  padding: 16px 35px !important;
  border-radius: 50px !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
  box-shadow: 0 15px 30px rgba(38, 224, 127, 0.2) !important;
  overflow: hidden;
  margin-top: 10px;
  width: auto !important;
  min-width: 200px;
  outline: none !important;
  text-transform: none !important;
  letter-spacing: 0 !important;
}

.submit-button:hover, 
.submit-button:focus,
.submit-button:active {
  transform: translateY(-5px) !important;
  box-shadow: 0 20px 40px rgba(38, 224, 127, 0.3) !important;
  background: var(--primary-gradient) !important;
  color: white !important;
  border: none !important;
  outline: none !important;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: all 0.6s ease;
  z-index: 1;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button i {
  font-size: 1rem;
  transition: all 0.3s ease;
}

.submit-button:hover i {
  transform: translateX(5px);
}

/* Mobile-specific styles for the submission button */
@media (max-width: 767px) {
  .submit-button {
    width: 100% !important;
    padding: 14px 20px !important;
    font-size: 1rem !important;
  }
}

/* Responsive Adjustments */
@media (max-width: 991px) {
  .contact-hero-2025 {
    min-height: auto;
    padding: 100px 0 60px;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .main-circle {
    width: 280px;
    height: 280px;
    margin: 30px auto;
  }
  
  .main-image {
    width: 150px;
    height: 150px;
  }
  
  .contact-method-item {
    min-width: 100%;
  }
  
  .form-container {
    padding: 30px 20px;
  }
}

@media (max-width: 767px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-description {
    font-size: 1.1rem;
    max-width: 100%;
  }
  
  .contact-visual {
    margin-top: 50px;
  }
  
  .main-circle {
    width: 240px;
    height: 240px;
  }
  
  .main-image {
    width: 120px;
    height: 120px;
  }
  
  .contact-icons .icon-item {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .channel-item {
    min-width: calc(50% - 15px);
  }
  
  .working-hours {
    padding: 30px 20px;
  }
  
  /* Mobile specific fixes */
  .contact-info-card {
    margin-left: 0;
    margin-right: 0;
  }
  
  .map-container iframe {
    width: 100% !important;
  }
  
  .contact-methods {
    flex-direction: column;
  }
  
  .contact-hero-content {
    padding-left: 0;
    padding-right: 0;
  }

  /* Adjust padding for mobile */
  section .container {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  /* Fix for form padding */
  .form-container {
    padding: 20px 15px;
  }
}

/* Extra small devices */
@media (max-width: 576px) {
  .hero-title {
    font-size: 1.75rem;
  }
  
  .contact-channels {
    flex-wrap: wrap;
  }
  
  .channel-item {
    min-width: calc(50% - 10px);
    padding: 15px 10px;
  }
  
  .contact-hero-2025 {
    padding: 80px 0 40px;
  }
  
  .form-floating > .form-control, 
  .form-floating > .form-select {
    height: calc(3.2rem + 2px);
  }
  
  .submit-button {
    width: 100% !important;
  }
  
  .contact-info-card {
    padding: 20px 15px !important;
  }
}

  