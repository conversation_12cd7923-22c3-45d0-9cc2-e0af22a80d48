<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تواصل معنا - مصنع منتجات الأسنان</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Animate CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="contact.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700;800&display=swap" rel="stylesheet">
  
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar-simple">
        <div class="container">
            <!-- Brand -->
            <a class="navbar-brand-simple" href="index.html">
                <div class="brand-logo">
                    <img src="https://img.icons8.com/ios-filled/50/tooth.png" alt="Logo">
                </div>
                <span class="brand-title">مصنع منتجات الأسنان</span>
            </a>
            
            <!-- Menu -->
            <ul class="nav-list" id="navList">
                <li class="nav-item-simple">
                    <a class="nav-link-simple" href="index.html">الرئيسية</a>
                </li>
                <li class="nav-item-simple">
                    <a class="nav-link-simple" href="about.html">من نحن</a>
                </li>
                <li class="nav-item-simple">
                    <a class="nav-link-simple" href="services.html">منتجاتنا</a>
                </li>
                <li class="nav-item-simple">
                    <a class="nav-link-simple active" href="contact.html">اتصل بنا</a>
                </li>
                <li class="nav-item-simple">
                    <a href="#order" class="cta-button">
                        <span>اطلب الآن</span>
                        <i class="fas fa-arrow-left"></i>
                    </a>
                </li>
            </ul>
            
            <!-- CTA Button على اليسار -->
            <a href="#order" class="cta-button">
                <span>اطلب الآن</span>
                <i class="fas fa-arrow-left"></i>
            </a>
            
            <!-- Mobile Toggle -->
            <button class="mobile-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="contact-hero-2025">
        <!-- Decorative elements -->
        <div class="hero-blob-1"></div>
        <div class="hero-blob-2"></div>
        <div class="hero-pattern"></div>
        
        <div class="container position-relative">
            <div class="row align-items-center">
                <div class="col-lg-6 mb-5 mb-lg-0">
                    <div class="contact-hero-content animate__animated animate__fadeIn">
                        <span class="contact-badge">تواصل معنا</span>
                        <h1 class="hero-title">
                            نحن <span class="gradient-text">نستمع</span>
                            <br>إليك دائماً
                        </h1>
                        <p class="hero-description">فريقنا جاهز للرد على استفساراتك وتقديم المساعدة السريعة لتلبية احتياجاتك من منتجاتنا</p>
                        
                        <div class="contact-methods">
                            <div class="contact-method-item">
                                <div class="method-icon">
                                    <i class="fas fa-phone-alt"></i>
                                </div>
                                <div class="method-content">
                                    <h6>اتصل بنا</h6>
                                    <p>****** 567 890</p>
                                </div>
                            </div>
                            
                            <div class="contact-method-item">
                                <div class="method-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="method-content">
                                    <h6>البريد الإلكتروني</h6>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                        </div>
                        
                        <a href="#contact-form" class="btn-hero-primary">
                            <span class="btn-text">ارسل استفسارك</span>
                            <span class="btn-icon"><i class="fas fa-arrow-left"></i></span>
                            <div class="btn-shine"></div>
                        </a>
                    </div>
                </div>
                
                <div class="col-lg-6">
                    <div class="contact-visual animate__animated animate__fadeIn">
                        <div class="visual-container">
                            <div class="main-circle">
                                <img src="https://img.icons8.com/ios-filled/200/26e07f/communication.png" alt="Contact" class="main-image">
                            </div>
                            
                            <div class="contact-icons">
                                <div class="icon-item phone">
                                    <i class="fas fa-phone-alt"></i>
                                </div>
                                <div class="icon-item email">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="icon-item chat">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div class="icon-item location">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="contact-channels">
                <a href="#" class="channel-item whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    <span>واتساب</span>
                </a>
                <a href="#" class="channel-item email">
                    <i class="fas fa-envelope"></i>
                    <span>البريد</span>
                </a>
                <a href="#" class="channel-item chat">
                    <i class="fas fa-comment-dots"></i>
                    <span>محادثة</span>
                </a>
                <a href="#" class="channel-item location">
                    <i class="fas fa-map-marked-alt"></i>
                    <span>العنوان</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Info -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="contact-info-card p-4 text-center">
                        <div class="contact-icon">
                            <img src="https://img.icons8.com/ios-filled/50/26e07f/phone.png" alt="Phone"/>
                        </div>
                        <h5 class="mb-3">اتصل بنا</h5>
                        <p class="mb-1 d-flex align-items-center justify-content-center">
                            <i class="fas fa-phone-alt me-2 text-primary"></i>
                            ****** 567 890
                        </p>
                        <p class="mb-0 d-flex align-items-center justify-content-center">
                            <i class="fas fa-headset me-2 text-primary"></i>
                            ****** 567 891
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="contact-info-card p-4 text-center">
                        <div class="contact-icon">
                            <img src="https://img.icons8.com/ios-filled/50/26e07f/mail.png" alt="Email"/>
                        </div>
                        <h5 class="mb-3">البريد الإلكتروني</h5>
                        <p class="mb-1 d-flex align-items-center justify-content-center">
                            <i class="fas fa-envelope me-2 text-primary"></i>
                            <EMAIL>
                        </p>
                        <p class="mb-0 d-flex align-items-center justify-content-center">
                            <i class="fas fa-paper-plane me-2 text-primary"></i>
                            <EMAIL>
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="contact-info-card p-4 text-center">
                        <div class="contact-icon">
                            <img src="https://img.icons8.com/ios-filled/50/26e07f/marker.png" alt="Location"/>
                        </div>
                        <h5 class="mb-3">العنوان</h5>
                        <p class="mb-0 d-flex align-items-center justify-content-center">
                            <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                            123 شارع الصناعة، المنطقة الصناعية
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form & Map -->
    <section id="contact-form" class="py-5 bg-light">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-6">
                    <div class="contact-form form-container">
                        <div class="mb-4">
                            <span class="badge bg-primary px-3 py-2 rounded-pill mb-2">تواصل معنا</span>
                            <h3 class="gradient-text">استفسار عن المنتجات</h3>
                            <p>املأ النموذج التالي وسيقوم فريقنا بالرد عليك في أقرب وقت ممكن</p>
                        </div>
                        <form>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="companyName" placeholder="اسم الشركة/العيادة" required>
                                        <label for="companyName">اسم الشركة/العيادة</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" class="form-control" id="email" placeholder="البريد الإلكتروني" required>
                                        <label for="email">البريد الإلكتروني</label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-floating">
                                        <select class="form-select" id="productType" required>
                                            <option value="" selected disabled>اختر نوع المنتجات</option>
                                            <option>منتجات زراعة الأسنان</option>
                                            <option>أدوات تنظيف وتعقيم</option>
                                            <option>مواد تجميلية وتبييض</option>
                                            <option>مواد حشو وتبطين</option>
                                            <option>أدوات معملية</option>
                                        </select>
                                        <label for="productType">نوع المنتجات المطلوبة</label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-floating">
                                        <textarea class="form-control" id="message" style="height: 150px" placeholder="تفاصيل الاستفسار" required></textarea>
                                        <label for="message">تفاصيل الاستفسار</label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="submit-button">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        إرسال الاستفسار
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="map-container h-100">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3452.469981368283!2d31.2357!3d30.0444!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzDCsDAyJzM5LjgiTiAzMcKwMTQnMDguNSJF!5e0!3m2!1sen!2seg!4v1620000000000!5m2!1sen!2seg" 
                                style="border:0; width:100%; height:100%; min-height: 450px;" allowfullscreen="" loading="lazy"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Working Hours -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="working-hours text-center">
                        <div class="mb-4">
                            <div class="d-inline-block p-3 rounded-circle bg-white shadow-sm">
                                <i class="fas fa-clock fa-3x text-primary"></i>
                            </div>
                            <h3 class="mt-4 mb-4 gradient-text">ساعات العمل</h3>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="p-3 mb-3 rounded-4 glass-effect">
                                    <p class="mb-1"><strong>الأحد - الخميس:</strong> 8:00 صباحاً - 5:00 مساءً</p>
                                </div>
                                <div class="p-3 mb-3 rounded-4 glass-effect">
                                    <p class="mb-1"><strong>الجمعة:</strong> مغلق</p>
                                </div>
                                <div class="p-3 mb-3 rounded-4 glass-effect">
                                    <p class="mb-1"><strong>السبت:</strong> 9:00 صباحاً - 2:00 مساءً</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="p-3 mb-3 rounded-4 glass-effect">
                                    <p class="mb-1"><strong>قسم المبيعات:</strong> 8:00 صباحاً - 6:00 مساءً</p>
                                </div>
                                <div class="p-3 mb-3 rounded-4 glass-effect">
                                    <p class="mb-1"><strong>خدمة العملاء:</strong> 8:00 صباحاً - 8:00 مساءً</p>
                                </div>
                                <div class="p-3 mb-3 rounded-4 glass-effect">
                                    <p class="mb-1"><strong>الشحن والتوزيع:</strong> 9:00 صباحاً - 4:00 مساءً</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Media -->
    <section class="py-5 bg-light">
        <div class="container text-center">
            <h3 class="mb-4 gradient-text">تابعنا على وسائل التواصل الاجتماعي</h3>
            <div class="d-flex justify-content-center gap-4">
                <a href="#" class="social-icon btn btn-outline-primary rounded-circle p-3">
                    <i class="fab fa-facebook-f fa-lg"></i>
                </a>
                <a href="#" class="social-icon btn btn-outline-primary rounded-circle p-3">
                    <i class="fab fa-instagram fa-lg"></i>
                </a>
                <a href="#" class="social-icon btn btn-outline-primary rounded-circle p-3">
                    <i class="fab fa-twitter fa-lg"></i>
                </a>
                <a href="#" class="social-icon btn btn-outline-primary rounded-circle p-3">
                    <i class="fab fa-youtube fa-lg"></i>
                </a>
                <a href="#" class="social-icon btn btn-outline-primary rounded-circle p-3">
                    <i class="fab fa-linkedin-in fa-lg"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="gradient-text">الأسئلة الشائعة للتواصل</h2>
                <p>إجابات على الأسئلة المتكررة حول طرق التواصل والطلبات</p>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1" aria-expanded="true">
                                    <i class="fas fa-question-circle me-2 text-primary"></i>
                                    كم تستغرق عملية الرد على الاستفسارات؟
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نلتزم بالرد على جميع الاستفسارات خلال 24 ساعة عمل كحد أقصى. في بعض الحالات قد نحتاج إلى وقت إضافي للتشاور مع الأقسام المتخصصة.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    <i class="fas fa-question-circle me-2 text-primary"></i>
                                    هل يمكنني زيارة المصنع شخصياً؟
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، يمكنك زيارة المصنع بعد تحديد موعد مسبق مع قسم المبيعات. نرحب بزيارات العملاء للتعرف على عمليات التصنيع ومعاينة جودة منتجاتنا.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                    <i class="fas fa-question-circle me-2 text-primary"></i>
                                    كيف يمكنني طلب عينات من منتجاتكم؟
                                </button>
                            </h2>
                            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك طلب عينات من منتجاتنا عبر نموذج الاتصال الموجود في هذه الصفحة، أو التواصل مع قسم المبيعات مباشرة. نوفر عينات مجانية للعملاء الجدد والحاليين.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="glass-effect p-5 text-center rounded-4">
                        <h3 class="gradient-text mb-4">اشترك في نشرتنا البريدية</h3>
                        <p class="mb-4">احصل على آخر أخبار المنتجات والعروض الحصرية مباشرة إلى بريدك الإلكتروني</p>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="email" class="form-control form-control-lg" placeholder="البريد الإلكتروني">
                                    <button class="btn btn-primary newsletter-btn" type="button">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        اشترك
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-2025">
        <div class="footer-waves">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" preserveAspectRatio="none">
                <path fill="rgba(38, 224, 127, 0.1)" fill-opacity="1" d="M0,160L48,149.3C96,139,192,117,288,112C384,107,480,117,576,144C672,171,768,213,864,208C960,203,1056,149,1152,133.3C1248,117,1344,139,1392,149.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
            </svg>
        </div>
        <div class="footer-content">
            <div class="container">
                <div class="row g-4">
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-brand">
                            <div class="footer-logo">
                                <div class="footer-logo-icon">
                                    <img src="https://img.icons8.com/ios-filled/50/ffffff/tooth.png" alt="Logo">
                                </div>
                                <h4 class="footer-logo-text">مصنع منتجات الأسنان</h4>
                            </div>
                            <p class="footer-description">
                                نصنع أفضل منتجات طب الأسنان بأحدث التقنيات العالمية لضمان أعلى مستويات الجودة والكفاءة
                            </p>
                            <div class="footer-social">
                                <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                                <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-links">
                            <h5 class="footer-heading">روابط سريعة</h5>
                            <ul class="list-unstyled">
                                <li><a href="index.html">الرئيسية</a></li>
                                <li><a href="about.html">من نحن</a></li>
                                <li><a href="services.html">منتجاتنا</a></li>
                                <li><a href="contact.html">اتصل بنا</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-5 col-md-12">
                        <div class="footer-contact">
                            <h5 class="footer-heading">اتصل بنا</h5>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <div class="icon-container">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="contact-text">
                                        <p>123 شارع الصناعة، المنطقة الصناعية</p>
                                    </div>
                                </div>
                                <div class="contact-item">
                                    <div class="icon-container">
                                        <i class="fas fa-phone-alt"></i>
                                    </div>
                                    <div class="contact-text">
                                        <p>****** 567 890</p>
                                    </div>
                                </div>
                                <div class="contact-item">
                                    <div class="icon-container">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="contact-text">
                                        <p><EMAIL></p>
                                    </div>
                                </div>
                            </div>
                            <div class="newsletter-form">
                                <div class="form-container">
                                    <input type="email" class="form-control" placeholder="البريد الإلكتروني">
                                    <button type="submit"><i class="fas fa-paper-plane"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <div class="copyright">
                    <p>© 2025 مصنع منتجات الأسنان. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>

    <!-- Custom JS -->
    <script>
        // Initialize AOS animation library
        AOS.init({
            duration: 800,
            once: true,
            easing: 'ease-out'
        });
        
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar-simple');
            if (navbar && window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else if (navbar) {
                navbar.classList.remove('scrolled');
            }
        });
        
        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Simple navbar toggle (sidebar)
            const menuToggle = document.getElementById('menuToggle');
            const navList = document.getElementById('navList');
            
            if (menuToggle && navList) {
                menuToggle.addEventListener('click', function() {
                    navList.classList.toggle('show');
                    this.querySelector('i').classList.toggle('fa-bars');
                    this.querySelector('i').classList.toggle('fa-times');
                });
                
                // Close menu when clicking on a link
                const navLinks = document.querySelectorAll('.nav-link-simple');
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        if (window.innerWidth < 992) {
                            navList.classList.remove('show');
                            menuToggle.querySelector('i').classList.add('fa-bars');
                            menuToggle.querySelector('i').classList.remove('fa-times');
                        }
                    });
                });
                
                // Add window resize event to hide menu on larger screens
                window.addEventListener('resize', function() {
                    if (window.innerWidth >= 992) {
                        navList.classList.remove('show');
                        menuToggle.querySelector('i').classList.add('fa-bars');
                        menuToggle.querySelector('i').classList.remove('fa-times');
                    }
                });
            }
            
            // Contact quick links hover effect
            const quickLinks = document.querySelectorAll('.quick-link');
            
            quickLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    const icon = this.querySelector('i');
                    icon.classList.add('animate__animated', 'animate__heartBeat');
                    
                    icon.addEventListener('animationend', function handler() {
                        icon.classList.remove('animate__animated', 'animate__heartBeat');
                        icon.removeEventListener('animationend', handler);
                    });
                });
            });
            
            // Animate contact icons on load
            const animateCSS = (element, animation, prefix = 'animate__') =>
                new Promise((resolve) => {
                    const animationName = `${prefix}${animation}`;
                    const node = document.querySelector(element);
                    
                    if (!node) {
                        console.log(`Element ${element} not found, skipping animation`);
                        resolve('Element not found');
                        return;
                    }
                    
                    node.classList.add(`${prefix}animated`, animationName);
                    
                    function handleAnimationEnd(event) {
                        event.stopPropagation();
                        node.classList.remove(`${prefix}animated`, animationName);
                        resolve('Animation ended');
                    }
                    
                    node.addEventListener('animationend', handleAnimationEnd, {once: true});
                });
            
            // Apply animations only to elements that exist
            setTimeout(() => {
                // Check if hero elements exist before animating
                const heroContent = document.querySelector('.contact-hero-content');
                if (heroContent) {
                    // These selectors should be updated to match what exists in your HTML
                    const badge = document.querySelector('.contact-badge');
                    const title = document.querySelector('.hero-title');
                    const description = document.querySelector('.hero-description');
                    const methods = document.querySelector('.contact-methods');
                    
                    if (badge) animateCSS('.contact-badge', 'fadeIn');
                    if (title) animateCSS('.hero-title', 'fadeInUp');
                    if (description) animateCSS('.hero-description', 'fadeInUp');
                    if (methods) setTimeout(() => animateCSS('.contact-methods', 'fadeInUp'), 400);
                    
                    // Only animate the CTA button if it exists
                    const ctaButton = document.querySelector('.btn-hero-primary');
                    if (ctaButton) setTimeout(() => animateCSS('.btn-hero-primary', 'fadeInUp'), 400);
                    
                    // Animate the visual if it exists
                    const contactVisual = document.querySelector('.contact-visual');
                    if (contactVisual) setTimeout(() => animateCSS('.contact-visual', 'zoomIn'), 600);
                    
                    // Animate contact channels if they exist
                    const contactChannels = document.querySelector('.contact-channels');
                    if (contactChannels) setTimeout(() => animateCSS('.contact-channels', 'fadeInUp'), 1000);
                }
            }, 300);
        });
    </script>
</body>
</html> 