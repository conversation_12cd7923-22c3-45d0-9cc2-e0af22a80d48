/* About Page Specific Styles */

/* Modern About Hero Section */
.about-hero-section {
    position: relative;
    padding: 120px 0 80px;
    min-height: 90vh;
    background: linear-gradient(135deg, #f0feff 0%, #f8fdfa 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    margin-top: 25px;
}

/* 3D Geometric Shapes */
.about-hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.about-shape {
    position: absolute;
    animation: floatAnimation 15s infinite ease-in-out;
    opacity: 0.4;
}

.about-shape-1 {
    top: 15%;
    left: 10%;
    width: 120px;
    height: 120px;
    border-radius: 30%;
    background: linear-gradient(45deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    transform: rotate(45deg);
    animation-delay: 0s;
}

.about-shape-2 {
    top: 70%;
    left: 5%;
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, rgba(38, 224, 127, 0.05) 0%, rgba(19, 197, 193, 0.05) 100%);
    border-radius: 70% 30% 30% 70% / 60% 40% 60% 40%;
    animation-delay: 3s;
}

.about-shape-3 {
    top: 25%;
    right: 5%;
    width: 180px;
    height: 180px;
    background: linear-gradient(45deg, rgba(19, 197, 193, 0.05) 0%, rgba(38, 224, 127, 0.05) 100%);
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    animation-delay: 1s;
    animation-duration: 18s;
}

.about-shape-4 {
    bottom: 10%;
    right: 15%;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, rgba(38, 224, 127, 0.08) 0%, rgba(19, 197, 193, 0.08) 100%);
    border-radius: 50%;
    animation-delay: 2s;
}

@keyframes floatAnimation {
    0% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-15px, 15px) rotate(5deg); }
    50% { transform: translate(0, 30px) rotate(0deg); }
    75% { transform: translate(15px, 15px) rotate(-5deg); }
    100% { transform: translate(0, 0) rotate(0deg); }
}

/* Hero Row */
.about-hero-row {
    position: relative;
    z-index: 5;
}

/* Hero Content */
.about-hero-content-wrapper {
    padding: 0 20px;
}

.about-hero-content {
    max-width: 580px;
}

.about-hero-badge {
    display: inline-block;
    background: rgba(38, 224, 127, 0.1);
    color: var(--primary);
    font-size: 1rem;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 30px;
    margin-bottom: 20px;
    position: relative;
}

.about-hero-badge::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: var(--primary);
    border-radius: 50%;
    margin-left: 8px;
}

.about-hero-title {
    font-size: 3.2rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 20px;
    color: #222;
}

.about-hero-description {
    font-size: 1.1rem;
    margin-bottom: 30px;
    color: #666;
    line-height: 1.8;
}

/* Hero Buttons */
.about-hero-buttons {
    display: flex;
    gap: 15px;
    margin-bottom: 40px;
}

.btn-about-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: var(--primary-gradient);
    color: white;
    font-weight: 700;
    padding: 14px 30px;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-about-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(38, 224, 127, 0.3);
    color: white;
}

.btn-about-primary::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    transform: rotate(30deg);
    transition: all 1.5s cubic-bezier(0.19, 1, 0.22, 1);
    opacity: 0;
}

.btn-about-primary:hover::after {
    opacity: 1;
    left: 100%;
}

.btn-about-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: #333;
    font-weight: 600;
    padding: 14px 30px;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(38, 224, 127, 0.2);
}

.btn-about-secondary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
    color: var(--primary);
    border-color: var(--primary);
}

/* Timeline Component */
.about-hero-timeline {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30px;
    padding: 20px 0;
}

.timeline-progress {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba(38, 224, 127, 0.2);
    transform: translateY(-50%);
    z-index: 1;
}

.timeline-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 25%;
    height: 100%;
    background: var(--primary-gradient);
    z-index: 2;
}

.timeline-point {
    position: relative;
    z-index: 3;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeline-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid rgba(38, 224, 127, 0.3);
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.timeline-point.active .timeline-dot,
.timeline-point:hover .timeline-dot {
    background: var(--primary);
    transform: scale(1.3);
    border-color: var(--primary);
    box-shadow: 0 0 0 5px rgba(38, 224, 127, 0.2);
}

.timeline-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    opacity: 0.7;
    transition: all 0.3s ease;
    transform: translateY(0);
}

.timeline-point.active .timeline-content,
.timeline-point:hover .timeline-content {
    opacity: 1;
    transform: translateY(-5px);
}

.timeline-year {
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 2px;
}

.timeline-content p {
    font-size: 0.85rem;
    margin: 0;
}

/* Visual Elements */
.about-hero-visual-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.about-hero-visual {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.about-hero-main-image {
    position: relative;
    z-index: 2;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
    border: 5px solid #fff;
    transition: all 0.5s ease;
}

.about-hero-main-image:hover {
    transform: scale(1.02) translateY(-10px);
    box-shadow: 0 30px 60px rgba(38, 224, 127, 0.2);
}

.main-about-image {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.5s ease;
}

.about-hero-main-image:hover .main-about-image {
    transform: scale(1.05);
}

/* Floating Elements */
.about-floating-element {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    z-index: 3;
    transition: all 0.5s ease;
    animation: floatElement 5s infinite ease-in-out;
}

.about-element-1 {
    top: -30px;
    right: -30px;
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 220px;
    animation-delay: 0s;
}

.about-element-2 {
    bottom: 20px;
    right: -40px;
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 220px;
    animation-delay: 1s;
}

.about-element-3 {
    top: 50%;
    left: -30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    animation-delay: 2s;
}

.about-element-4 {
    bottom: -30px;
    left: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation-delay: 1.5s;
}

@keyframes floatElement {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

.about-floating-element:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 25px 50px rgba(38, 224, 127, 0.15);
    z-index: 4;
}

.about-element-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    color: white;
    font-size: 1.3rem;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.rotate-element {
    animation: rotateIcon 10s linear infinite;
}

@keyframes rotateIcon {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.about-element-content {
    flex: 1;
}

.about-element-content h5 {
    font-size: 1rem;
    margin: 0 0 5px;
    font-weight: 700;
}

.certification-stars {
    color: #FFD700;
    font-size: 0.8rem;
}

.about-element-progress {
    height: 5px;
    width: 100%;
    background: rgba(38, 224, 127, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-bar {
    height: 100%;
    width: 85%;
    background: var(--primary-gradient);
    border-radius: 10px;
    animation: progressAnimation 2.5s ease-out;
}

@keyframes progressAnimation {
    0% { width: 0; }
    100% { width: 85%; }
}

.about-element-digits {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.digit-counter {
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary);
    position: relative;
}

.digit-counter::after {
    content: '+';
    position: absolute;
    top: 0;
    right: -10px;
    font-size: 1.2rem;
}

.digit-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

/* Responsive Styles */
@media (max-width: 1199px) {
    .about-hero-title {
        font-size: 2.8rem;
    }
    
    .about-floating-element {
        transform: scale(0.9);
    }
    
    .about-element-1 {
        right: -20px;
    }
    
    .about-element-2 {
        right: -20px;
    }
}

@media (max-width: 991px) {
    .about-hero-section {
        padding: 80px 0;
    }
    
    .about-hero-content-wrapper {
        order: 1;
        margin-bottom: 60px;
    }
    
    .about-hero-visual-wrapper {
        order: 2;
    }
    
    .about-hero-content {
        max-width: 100%;
        text-align: center;
        margin: 0 auto;
    }
    
    .about-hero-badge::before {
        margin: 0 5px;
    }
    
    .about-hero-buttons {
        justify-content: center;
    }
    
    .about-hero-title {
        font-size: 2.5rem;
    }
    
    .about-floating-element {
        position: relative;
        margin: 10px;
        top: auto;
        right: auto;
        bottom: auto;
        left: auto;
        transform: none;
        animation: none;
    }
    
    .about-hero-visual {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
    }
    
    .about-hero-main-image {
        width: 100%;
        max-width: 400px;
        margin-bottom: 30px;
    }
}

@media (max-width: 767px) {
    .about-hero-title {
        font-size: 2rem;
    }
    
    .about-hero-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .about-hero-timeline {
        flex-wrap: wrap;
        justify-content: center;
        gap: 30px;
    }
    
    .timeline-progress {
        display: none;
    }
    
    .timeline-point {
        width: 45%;
    }
}

/* ===== OUR STORY SECTION - MODERN 2025 STYLING ===== */
.our-story-section {
    position: relative;
    padding: 100px 0;
    overflow: hidden;
    z-index: 1;
}

.our-story-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(240, 254, 255, 0.5) 0%, rgba(248, 253, 250, 0.5) 100%);
    z-index: -1;
    clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}

.our-story-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.our-story-shape {
    position: absolute;
    opacity: 0.4;
    border-radius: 50%;
}

.our-story-shape-1 {
    top: 10%;
    right: 10%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.03) 0%, rgba(255, 255, 255, 0) 70%);
    animation: pulse-slow 10s infinite alternate;
}

.our-story-shape-2 {
    bottom: 15%;
    left: 5%;
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.03) 0%, rgba(255, 255, 255, 0) 70%);
    animation: pulse-slow 8s infinite alternate-reverse;
}

@keyframes pulse-slow {
    0% { transform: scale(1); opacity: 0.3; }
    100% { transform: scale(1.5); opacity: 0.1; }
}

/* Image Container */
.story-image-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px;
}

.story-image-container {
    position: relative;
    border-radius: 30px;
    overflow: hidden;
    box-shadow: 0 30px 70px rgba(0, 0, 0, 0.1);
    transform-style: preserve-3d;
    perspective: 1000px;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.story-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.story-image-container:hover::before {
    opacity: 1;
}

.story-image-container img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}

.story-image-container:hover img {
    transform: scale(1.05);
}

.story-badge {
    position: absolute;
    top: -25px;
    left: -25px;
    z-index: 2;
    transition: all 0.3s ease;
}

.story-badge-inner {
    width: 70px;
    height: 70px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 15px 35px rgba(38, 224, 127, 0.3);
    animation: float-badge 5s infinite ease-in-out;
}

@keyframes float-badge {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-15px); }
}

.story-badge-inner i {
    font-size: 28px;
    color: white;
}

.story-image-decorations {
    position: absolute;
    z-index: 3;
}

.story-decoration {
    position: absolute;
    background: white;
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    backdrop-filter: blur(10px);
}

.story-decoration-1 {
    bottom: 30px;
    right: -20px;
    animation: floatElement 5s infinite ease-in-out;
    z-index: 5;
}

.story-decoration-2 {
    top: -30px;
    right: 120px;
    animation: floatElement 5s infinite ease-in-out reverse;
    animation-delay: 2s;
    z-index: 5;
}

.decoration-icon {
    width: 40px;
    height: 40px;
    background: rgba(38, 224, 127, 0.1);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary);
    font-size: 1.2rem;
}

.decoration-content h6 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 700;
}

.decoration-content p {
    margin: 0;
    font-size: 0.8rem;
    color: #666;
}

/* Content Styling */
.story-content-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px;
}

.story-title {
    position: relative;
    display: inline-block;
    margin-bottom: 20px;
    font-size: 2.5rem;
    font-weight: 800;
}

.story-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 4px;
}

.story-lead {
    font-size: 1.25rem;
    font-weight: 500;
    color: #444;
    margin-bottom: 25px;
}

.story-text {
    color: #666;
    margin-bottom: 30px;
    line-height: 1.8;
}

/* Feature Item Styling */
.story-feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 20px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(238, 238, 238, 0.5);
}

.story-feature-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(38, 224, 127, 0.1);
    border-color: rgba(38, 224, 127, 0.2);
}

.feature-icon-wrapper {
    position: relative;
    margin-left: 20px;
    flex-shrink: 0;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.2);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.story-feature-item:hover .feature-icon {
    transform: scale(1.1) rotate(10deg);
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    background: var(--primary-gradient);
    opacity: 0.2;
    border-radius: 22px;
    z-index: -1;
    transition: all 0.3s ease;
}

.story-feature-item:hover .feature-icon::after {
    opacity: 0.4;
    top: -12px;
    left: -12px;
    right: -12px;
    bottom: -12px;
}

.feature-icon i {
    font-size: 1.8rem;
    color: white;
}

.feature-content h5 {
    margin-bottom: 8px;
    font-weight: 700;
    color: #333;
    font-size: 1.1rem;
}

.feature-content p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .our-story-section {
        padding: 70px 0;
    }
    
    .story-badge {
        top: -15px;
        left: -15px;
    }
    
    .story-badge-inner {
        width: 60px;
        height: 60px;
    }
    
    .story-badge-inner i {
        font-size: 24px;
    }
}

@media (max-width: 767px) {
    .story-title {
        font-size: 2rem;
        text-align: center;
        display: block;
    }
    
  
    .story-lead, .story-text {
        text-align: center;
    }
    
    .story-image-wrapper {
        margin-bottom: 50px;
    }
    
    .story-decoration {
        display: none;
    }
}

/* Mission & Vision */
.mission-vision {
    background: linear-gradient(135deg, #eafafc 0%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 40px;
    margin: 30px 0;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.mission-vision::before {
    content: "";
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(38, 224, 127, 0.05);
    z-index: -1;
}

/* Team Members */
.team-member {
    transition: var(--transition);
    border-radius: var(--border-radius);
    padding: 2rem;
    background: #fff;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(230, 230, 230, 0.5);
}

.team-member:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-color);
}

.team-member img {
    transition: var(--transition);
    border: 5px solid rgba(38, 224, 127, 0.1);
}

.team-member:hover img {
    transform: scale(1.05);
    border-color: var(--primary-light);
}

/* Achievements */
.achievement-card {
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    background: #fff;
    box-shadow: var(--shadow-sm);
}

.achievement-card:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow: var(--shadow-color);
}


/* Facility Images */
.facility-image {
    transition: var(--transition);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    position: relative;
}

.facility-image img {
    transition: var(--transition);
}

.facility-image:hover img {
    transform: scale(1.05);
}

.facility-image h5 {
    position: relative;
    display: inline-block;
}

.facility-image h5::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 3px;
}

/* Certifications */
.certification {
    transition: var(--transition);
}

.certification:hover {
    transform: translateY(-10px);
}

.certification img {
    filter: drop-shadow(0 10px 15px rgba(38, 224, 127, 0.2));
    transition: var(--transition);
}

.certification:hover img {
    transform: scale(1.1) rotate(5deg);
}

/* Manufacturing Facilities Section 2025 Style */
.facility-section-2025 {
    position: relative;
    background: linear-gradient(135deg, #f0faff 0%, #f7fffa 100%);
    padding: 80px 0;
    overflow: hidden;
    margin-top: 30px;
}

.facility-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px 0;
}

.facility-shape-1 {
    position: absolute;
    top: -120px;
    right: -120px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
    animation: pulse-slow 15s infinite alternate;
    z-index: -1;
}

.facility-shape-2 {
    position: absolute;
    bottom: -150px;
    left: -150px;
    width: 450px;
    height: 450px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
    animation: pulse-slow 12s infinite alternate-reverse;
    z-index: -1;
}

.facility-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDMiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PHBhdGggZD0iTTIwIDIwaDIwdjIwSDIweiIvPjwvZz48L3N2Zz4=');
    z-index: -1;
    opacity: 0.3;
}

@keyframes blob-animation {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.facility-cards .row {
    --bs-gutter-x: 25px;
}

.facility-card {
    position: relative;
    height: 100%;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: 24px;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.7);
}

.facility-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(38, 224, 127, 0.1);
    border-color: rgba(38, 224, 127, 0.2);
}

.facility-card-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
}

.facility-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.7s ease;
}

.facility-card:hover .facility-card-image img {
    transform: scale(1.1);
}

.facility-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 70%);
    opacity: 0;
    transition: all 0.5s ease;
    display: flex;
    align-items: flex-end;
    padding: 20px;
}

.facility-card:hover .facility-overlay {
    opacity: 1;
}

.facility-stats {
    display: flex;
    gap: 15px;
    width: 100%;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 8px 15px;
    border-radius: 12px;
    min-width: 70px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

.facility-card-body {
    padding: 30px;
    position: relative;
}

.facility-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    transition: all 0.5s ease;
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.2);
}

.facility-card:hover .facility-icon {
    transform: scale(1.1) rotate(10deg);
}

.facility-icon i {
    font-size: 24px;
    color: white;
}

.facility-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    position: relative;
    display: inline-block;
}




.facility-desc {
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
}

.facility-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.facility-features li {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    transition: transform 0.3s ease;
}

.facility-features li:hover {
    transform: translateX(5px);
}

.facility-features li i {
    color: var(--primary);
    font-size: 14px;
}

.facility-card-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.05) 45%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 55%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    pointer-events: none;
    opacity: 0;
}

.facility-card:hover .facility-card-shine {
    animation: shine 1.5s forwards;
}

@keyframes shine {
    0% {
        opacity: 0;
        transform: translateX(-100%) rotate(30deg);
    }
    100% {
        opacity: 1;
        transform: translateX(100%) rotate(30deg);
    }
}

@media (max-width: 991px) {
    .facility-section-2025 {
        padding: 60px 0;
    }
    
    .facility-card {
        margin-bottom: 20px;
    }
    
    .facility-card-image {
        height: 180px;
    }
    
    .facility-card-body {
        padding: 25px;
    }
    
    .facility-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
    }
    
    .facility-icon i {
        font-size: 20px;
    }
    
    .facility-title {
        font-size: 1.3rem;
    }
}

@media (max-width: 767px) {
    .facility-card-image {
        height: 160px;
    }
    
    .facility-card-body {
        padding: 20px;
    }
    
    .facility-stats {
        gap: 10px;
    }
    
    .stat {
        min-width: 60px;
        padding: 5px 10px;
    }
    
    .stat-value {
        font-size: 1rem;
    }
    
    .stat-label {
        font-size: 0.7rem;
    }
}

/* Vision & Mission Section 2025 Style */
.vision-mission-section {
    position: relative;
    background: linear-gradient(135deg, #f5fbff 0%, #f9fffc 100%);
    padding: 80px 0;
    overflow: hidden;
}

.vm-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px 0;
}

.vm-shape-1 {
    position: absolute;
    top: -150px;
    right: -100px;
    width: 450px;
    height: 450px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.06) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 15s infinite linear;
    z-index: -1;
}

.vm-shape-2 {
    position: absolute;
    bottom: -150px;
    left: -100px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.06) 0%, rgba(255,255,255,0) 70%);
    border-radius: 61% 39% 33% 67% / 69% 56% 44% 31%;
    animation: blob-animation 15s infinite linear reverse;
    z-index: -1;
}

.vm-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDMiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiLz48L2c+PC9zdmc+');
    z-index: -1;
    opacity: 0.2;
}

.vision-card, .mission-card {
    position: relative;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: 24px;
    padding: 40px;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    border: 1px solid rgba(255, 255, 255, 0.7);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.vision-card {
    border-right: 4px solid rgba(38, 224, 127, 0.5);
}

.mission-card {
    border-right: 4px solid rgba(19, 197, 193, 0.5);
}

.vision-card:hover, .mission-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(38, 224, 127, 0.1);
}

.vision-card:hover {
    border-color: rgba(38, 224, 127, 0.8);
}

.mission-card:hover {
    border-color: rgba(19, 197, 193, 0.8);
}

.vision-icon, .mission-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    position: relative;
}

.vision-icon .icon-backdrop, .mission-icon .icon-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    z-index: -1;
    animation: pulse-slow 3s infinite alternate;
}

.vision-icon .icon-backdrop {
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.12) 0%, rgba(38, 224, 127, 0.05) 100%);
}

.mission-icon .icon-backdrop {
    background: linear-gradient(135deg, rgba(19, 197, 193, 0.12) 0%, rgba(19, 197, 193, 0.05) 100%);
}

.vision-icon i, .mission-icon i {
    font-size: 36px;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.vision-icon i {
    background: linear-gradient(135deg, #26e07f 0%, #1fc476 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.mission-icon i {
    background: linear-gradient(135deg, #13c5c1 0%, #11b6b3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.vision-card:hover .vision-icon i, .mission-card:hover .mission-icon i {
    transform: scale(1.1) rotate(5deg);
}

.vision-title, .mission-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 25px;
    position: relative;
    display: inline-block;
}

.vision-title {
    color: #26e07f;
}

.mission-title {
    color: #13c5c1;
}

.vision-decoration, .mission-decoration {
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    border-radius: 0 24px 0 100px;
    opacity: 0.1;
    transition: all 0.5s ease;
    z-index: 0;
}

.vision-decoration {
    background: linear-gradient(135deg, #26e07f 0%, transparent 80%);
}

.mission-decoration {
    background: linear-gradient(135deg, #13c5c1 0%, transparent 80%);
}

.vision-card:hover .vision-decoration, .mission-card:hover .mission-decoration {
    opacity: 0.15;
    width: 150px;
    height: 150px;
}

.vision-desc, .mission-desc {
    color: #555;
    line-height: 1.7;
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.vision-features, .mission-features {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    position: relative;
    z-index: 1;
}

.feature {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateX(5px);
}

.feature-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.vision-card .feature-dot {
    background: linear-gradient(135deg, #26e07f 0%, #1fc476 100%);
}

.mission-card .feature-dot {
    background: linear-gradient(135deg, #13c5c1 0%, #11b6b3 100%);
}

.feature span {
    font-size: 0.95rem;
    color: #444;
    font-weight: 500;
}

@media (max-width: 991px) {
    .vision-mission-section {
        padding: 60px 0;
    }
    
    .vision-card, .mission-card {
        padding: 30px;
        margin-bottom: 20px;
    }
    
    .vision-icon, .mission-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
    }
    
    .vision-icon i, .mission-icon i {
        font-size: 30px;
    }
    
    .vision-title, .mission-title {
        font-size: 1.5rem;
        margin-bottom: 20px;
    }
    
    .vision-features, .mission-features {
        gap: 10px;
    }
}

@media (max-width: 767px) {
    .vision-card, .mission-card {
        padding: 25px;
    }
    
    .vision-features, .mission-features {
        flex-direction: column;
        gap: 8px;
    }
    
    .vision-decoration, .mission-decoration {
        width: 80px;
        height: 80px;
    }
}

/* Certifications Section 2025 Style */
.certifications-section {
    position: relative;
    background: linear-gradient(135deg, #f5fbfd 0%, #fafffe 100%);
    padding: 80px 0;
    overflow: hidden;
}

.certifications-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px 0;
}

.cert-shape-1 {
    position: absolute;
    top: -100px;
    left: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 70%);
    animation: pulse-slow 15s infinite alternate;
    z-index: -1;
}

.cert-shape-2 {
    position: absolute;
    bottom: -120px;
    right: -120px;
    width: 350px;
    height: 350px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.05) 0%, rgba(255,255,255,0) 70%);
    animation: pulse-slow 12s infinite alternate-reverse;
    z-index: -1;
}

.cert-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDMiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PHBhdGggZD0iTTIwIDIwaDIwdjIwSDIweiIvPjwvZz48L3N2Zz4=');
    z-index: -1;
    opacity: 0.25;
}

.section-subtitle {
    color: #666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto 20px;
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.certification-card {
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 24px;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 25px;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    border: 1px solid rgba(255, 255, 255, 0.7);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.03);
    overflow: hidden;
}

.certification-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.03) 0%, rgba(19, 197, 193, 0.03) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.certification-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(38, 224, 127, 0.08);
    border-color: rgba(38, 224, 127, 0.15);
}

.certification-card:hover::before {
    opacity: 1;
}

.certification-icon {
    position: relative;
    width: 90px;
    height: 90px;
    min-width: 90px;
    background: white;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(38, 224, 127, 0.1);
    transition: all 0.5s ease;
    z-index: 1;
}

.certification-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.3) 0%, rgba(255,255,255,0) 70%);
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.5s ease, transform 0.5s ease;
    z-index: -1;
}

.certification-card:hover .certification-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 15px 35px rgba(38, 224, 127, 0.15);
}

.certification-card:hover .certification-glow {
    opacity: 1;
    transform: scale(1.3);
}

.certification-icon img {
    width: 60px;
    height: 60px;
    transition: transform 0.5s ease;
    filter: drop-shadow(0 5px 10px rgba(38, 224, 127, 0.15));
}

.certification-card:hover .certification-icon img {
    transform: scale(1.15);
    filter: drop-shadow(0 8px 15px rgba(38, 224, 127, 0.25));
}

.certification-content {
    flex: 1;
}

.certification-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #333;
    position: relative;
    display: inline-block;
}



.certification-desc {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
}

@media (max-width: 991px) {
    .certifications-section {
        padding: 60px 0;
    }
    
    .certifications-grid {
        gap: 20px;
    }
    
    .certification-card {
        padding: 25px;
    }
    
    .certification-icon {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }
    
    .certification-icon img {
        width: 50px;
        height: 50px;
    }
    
    .certification-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 767px) {
    .certifications-grid {
        grid-template-columns: 1fr;
    }
    
    .certification-card {
        padding: 20px;
    }
    
    .certification-icon {
        width: 70px;
        height: 70px;
        min-width: 70px;
    }
    
    .certification-icon img {
        width: 40px;
        height: 40px;
    }
}

/* CTA Section 2025 Style */
.cta-section-2025 {
    position: relative;
    padding: 80px 0;
    overflow: hidden;
    background: linear-gradient(135deg, #f8fbff 0%, #f5fffd 100%);
    margin: 50px 0;
}

.cta-wrapper-2025 {
    position: relative;
    z-index: 2;
}

.cta-blob-1 {
    position: absolute;
    top: -150px;
    right: -100px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.07) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 15s infinite linear;
    z-index: -1;
}

.cta-blob-2 {
    position: absolute;
    bottom: -150px;
    left: -100px;
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.07) 0%, rgba(255,255,255,0) 70%);
    border-radius: 61% 39% 33% 67% / 69% 56% 44% 31%;
    animation: blob-animation 15s infinite linear reverse;
    z-index: -1;
}

.cta-dots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDMiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiLz48L2c+PC9zdmc+');
    z-index: -1;
    opacity: 0.5;
}

.cta-card-2025 {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.07);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.7);
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.cta-card-2025:hover {
    box-shadow: 0 30px 60px rgba(38, 224, 127, 0.12);
    transform: translateY(-5px);
    border-color: rgba(38, 224, 127, 0.2);
}

.cta-card-inner {
    position: relative;
    padding: 50px;
    z-index: 1;
}

.cta-badge {
    display: inline-block;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
    color: var(--primary);
    font-size: 1rem;
    font-weight: 600;
    padding: 8px 20px;
    border-radius: 30px;
    margin-bottom: 20px;
    position: relative;
}

.cta-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
    transform: translateX(-100%);
    animation: shine-badge 3s infinite;
    border-radius: 30px;
}

@keyframes shine-badge {
    0% { transform: translateX(-100%); }
    20%, 100% { transform: translateX(100%); }
}

.cta-title-2025 {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(to right, #222, #555);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    margin-bottom: 20px;
    line-height: 1.3;
}

.cta-desc {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 25px;
    line-height: 1.7;
}

.cta-features {
    list-style: none;
    padding: 0;
    margin: 0 0 25px;
}

.cta-features li {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    color: #444;
    font-size: 1.05rem;
    transition: transform 0.3s ease;
}

.cta-features li:hover {
    transform: translateX(8px);
}

.cta-features li i {
    color: var(--primary);
    margin-left: 15px;
    font-size: 1.1rem;
}

.cta-action-2025 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 25px;
    height: 100%;
}

.cta-icon {
    position: relative;
    width: 90px;
    height: 90px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 15px 35px rgba(38, 224, 127, 0.25);
    margin-bottom: 5px;
}

.cta-icon-pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: white;
    opacity: 0.7;
    animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(0.8);
        opacity: 0.7;
    }
    80%, 100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

.cta-icon i {
    color: white;
    font-size: 2.5rem;
    position: relative;
    z-index: 1;
    animation: pulse-icon 2s infinite;
}

@keyframes pulse-icon {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.btn-cta-2025 {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: var(--primary-gradient);
    color: white;
    font-weight: 700;
    padding: 15px 40px;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 15px 35px rgba(38, 224, 127, 0.25);
    overflow: hidden;
    width: 100%;
    max-width: 250px;
}

.btn-cta-2025:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 20px 40px rgba(38, 224, 127, 0.3);
    color: white;
}

.btn-cta-2025 .btn-text {
    position: relative;
    z-index: 1;
    font-size: 1.1rem;
}

.btn-cta-2025 .btn-icon {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.btn-cta-2025:hover .btn-icon {
    transform: translateX(-5px);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: translateX(-100%);
    animation: shine 3s infinite;
    z-index: 0;
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    20%, 100% { transform: translateX(100%); }
}

@media (max-width: 991px) {
    .cta-section-2025 {
        padding: 60px 0;
    }
    
    .cta-card-inner {
        padding: 40px 30px;
    }
    
    .cta-title-2025 {
        font-size: 2rem;
    }
    
    .cta-desc {
        font-size: 1rem;
    }
    
    .cta-action-2025 {
        margin-top: 30px;
        flex-direction: row;
        gap: 15px;
    }
    
    .cta-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 0;
    }
    
    .cta-icon i {
        font-size: 2rem;
    }
    
    .btn-cta-2025 {
        max-width: 200px;
    }
}

@media (max-width: 767px) {
    .cta-card-inner {
        padding: 30px 20px;
        text-align: center;
    }
    
    .cta-title-2025 {
        font-size: 1.8rem;
    }
    
    .cta-features li {
        justify-content: center;
    }
    
    .cta-action-2025 {
        flex-direction: column;
        margin-top: 20px;
    }
    
    .btn-cta-2025 {
        width: 100%;
        max-width: 100%;
    }
}

/* Achievements Section 2025 Style */
.achievements-section {
    position: relative;
    background: linear-gradient(135deg, #f7fbff 0%, #fdfdfd 100%);
    padding: 80px 0;
    overflow: hidden;
}

.achievements-wrapper {
    position: relative;
    z-index: 2;
    padding: 20px 0;
}

.achievement-blob {
    position: absolute;
    top: -150px;
    right: -150px;
    width: 450px;
    height: 450px;
    background: radial-gradient(circle, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
    animation: blob-animation 15s infinite linear;
    z-index: -1;
}

.achievement-blob-2 {
    position: absolute;
    bottom: -150px;
    left: -100px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(19, 197, 193, 0.05) 0%, rgba(255,255,255,0) 70%);
    border-radius: 61% 39% 33% 67% / 69% 56% 44% 31%;
    animation: blob-animation 15s infinite linear reverse;
    z-index: -1;
}

.achievement-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDMiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiLz48L2c+PC9zdmc+');
    z-index: -1;
    opacity: 0.3;
}

.achievement-card-2025 {
    position: relative;
    overflow: hidden;
    border-radius: 24px;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

.achievement-card-2025:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 50px rgba(38, 224, 127, 0.15);
    border-color: rgba(38, 224, 127, 0.2);
}

.achievement-card-inner {
    position: relative;
    padding: 40px 30px;
    z-index: 1;
    overflow: hidden;
}

.achievement-icon {
    position: relative;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    z-index: 1;
}

.icon-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.12) 0%, rgba(19, 197, 193, 0.12) 100%);
    animation: pulse-slow 3s infinite alternate;
    z-index: -1;
}

.achievement-icon i {
    font-size: 38px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    animation: float-icon 5s ease-in-out infinite;
}

@keyframes float-icon {
    0%, 100% { transform: translateY(0) rotate(0); }
    50% { transform: translateY(-5px) rotate(5deg); }
}

@keyframes pulse-slow {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(1.3); opacity: 0.7; }
}

.achievement-content {
    text-align: center;
}

.achievement-value {
    font-size: 0;
    margin-bottom: 10px;
    display: inline-block;
    position: relative;
}

.achievement-value .counter {
    font-size: 3.5rem;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    line-height: 1;
}

.achievement-value .plus {
    font-size: 2rem;
    font-weight: 600;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    position: absolute;
    top: 5px;
    right: -15px;
}

.achievement-title {
    color: #333;
    font-size: 1.4rem;
    margin-bottom: 15px;
    font-weight: 700;
    position: relative;
    display: inline-block;
}



.achievement-desc {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
    margin-top: 15px;
}

.achievement-decoration {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 80px;
    height: 80px;
    border-radius: 0 24px 0 80px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.03) 0%, rgba(19, 197, 193, 0.03) 100%);
    z-index: -1;
    transition: all 0.5s ease;
}

.achievement-card-2025:hover .achievement-decoration {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(38, 224, 127, 0.07) 0%, rgba(19, 197, 193, 0.07) 100%);
}

@media (max-width: 991px) {
    .achievements-section {
        padding: 60px 0;
    }
    
    .achievement-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 20px;
    }
    
    .achievement-icon i {
        font-size: 32px;
    }
    
    .achievement-value .counter {
        font-size: 3rem;
    }
    
    .achievement-value .plus {
        font-size: 1.8rem;
        right: -12px;
    }
    
    .achievement-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 767px) {
    .achievement-card-2025 {
        margin-bottom: 15px;
    }
    
    .achievement-card-inner {
        padding: 30px 20px;
    }
}

/* Team Member Social Icons */
.social-icons .btn-outline-primary:hover i {
    color: white !important;
}

/* الأيقونات الاجتماعية والأيقونات في جميع أنحاء الصفحة */
.social-icons .btn-outline-primary,
.separator-icon,
.footer-social .social-link,
.contact-item .icon-container,
.fab, .fas {
    color: var(--primary) !important;
}

/* تنسيق للأزرار عند المرور عليها */
.social-icons .btn-outline-primary:hover {
    background: var(--primary-gradient) !important;
    border-color: var(--primary) !important;
}

.social-icons .btn-outline-primary:hover i,
.btn-about-primary i,
.btn-cta-2025 .btn-icon i,
.navbar-cta .btn-icon i {
    color: white !important;
}

/* فقط الأيقونات البيضاء على خلفية خضراء */
.feature-icon i,
.facility-icon i {
    color: white !important;
}