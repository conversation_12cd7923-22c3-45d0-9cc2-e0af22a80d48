/* Global Variables */
:root {
  --primary: #26e07f;
  --primary-light: rgba(38, 224, 127, 0.15);
  --primary-gradient: linear-gradient(45deg, #26e07f, #13c5c1);
  --primary-glass: rgba(38, 224, 127, 0.1);
  --secondary: #6c757d;
  --light: #f9fafb;
  --dark: #212529;
  --accent: #eafafc;
  --shadow-sm: 0 4px 20px rgba(0, 0, 0, 0.03);
  --shadow-md: 0 10px 35px rgba(0, 0, 0, 0.05);
  --shadow-color: 0 15px 40px rgba(38, 224, 127, 0.15);
  --transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  --border-radius: 18px;
  --border-radius-sm: 12px;
  --border-radius-lg: 24px;
  
  /* 2025 Design Variables */
  --glass-bg: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  --glass-blur: 12px;
  --footer-bg: #111827;
  --footer-text: rgba(255, 255, 255, 0.8);
  --footer-heading: #ffffff;
  --footer-link: rgba(255, 255, 255, 0.6);
  --footer-link-hover: var(--primary);
  --social-hover: var(--primary-gradient);
  --nav-height: 80px;
}

/* Base Styles */
body {
  font-family: 'Cairo', sans-serif;
  background: var(--light);
  color: var(--dark);
  overflow-x: hidden;
  line-height: 1.7;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.3;
}

.display-4 {
  font-weight: 800;
  letter-spacing: -1px;
}

/* Navigation */
.navbar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background-color: rgba(255, 255, 255, 0.8) !important;
  transition: var(--transition);
  padding: 15px 0;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.03);
}

.navbar-brand {
  color: var(--primary) !important;
  font-size: 1.6rem;
  font-weight: 800;
  letter-spacing: -0.5px;
}

.nav-link {
  position: relative;
  font-weight: 600;
  padding: 0.8rem 1.2rem !important;
  transition: var(--transition);
  font-size: 1.05rem;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: var(--primary-gradient);
  transition: var(--transition);
  border-radius: 5px;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 60%;
}

.nav-link.active, 
.nav-link:focus, 
.nav-link:hover {
  color: var(--primary) !important;
}

/* Buttons */
.btn {
  border-radius: 50px !important;
  padding: 0.75rem 2rem !important;
  transition: var(--transition) !important;
  font-weight: 700 !important;
  letter-spacing: 0.3px;
  text-transform: none;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  transition: var(--transition);
}

.btn:hover::before {
  width: 100%;
}

.btn-primary, 
.btn-outline-primary:hover {
  background: var(--primary-gradient) !important;
  border: none !important;
  box-shadow: 0 10px 25px rgba(38, 224, 127, 0.25);
}

.btn-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(38, 224, 127, 0.3) !important;
}

.btn-outline-primary {
  color: var(--primary) !important;
  border: 2px solid var(--primary) !important;
  background: transparent !important;
}

/* Forms */
.form-control, .form-select {
  padding: 0.9rem 1.2rem;
  border-radius: var(--border-radius-sm);
  border: 1px solid #e8e8e8;
  transition: var(--transition);
  font-size: 1rem;
  background-color: #f9f9f9;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(38, 224, 127, 0.1);
  background-color: #ffffff;
}

/* Footer */
.footer-section {
  background: linear-gradient(to right, #212529, #343a40);
  color: #fff;
  border-top-left-radius: 50px;
  border-top-right-radius: 50px;
  margin-top: 50px;
  position: relative;
  overflow: hidden;
}

.footer-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://images.unsplash.com/photo-1606811971618-4486d14f3f99?auto=format&fit=crop&q=80') center no-repeat;
  background-size: cover;
  opacity: 0.02;
}

.footer-section h5, .footer-section h6 {
  color: var(--primary);
  margin-bottom: 20px;
}

.footer-section a {
  color: #e0e0e0;
  text-decoration: none;
  transition: var(--transition);
}

.footer-section a:hover {
  color: var(--primary);
  transform: translateX(5px);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.7s ease-out forwards;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Glassmorphism Effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius);
}

/* Media Queries */
@media (max-width: 767px) {
  .navbar {
    padding: 10px 0;
  }
  
  .btn {
    padding: 0.65rem 1.5rem !important;
  }
}

/* Bootstrap Color Overrides */
.bg-primary {
  background: var(--primary-gradient) !important;
}

.text-primary {
  color: var(--primary) !important;
}

.border-primary {
  border-color: var(--primary) !important;
}

/* Utility Classes */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.floating-card {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.floating-card:hover {
  transform: translateY(-15px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.1);
}

/* Hero Section */
.hero-section {
  min-height: 550px;
  background: linear-gradient(135deg, #eafafc 0%, #d6f5f2 100%);
  padding: 150px 0 120px;
  position: relative;
  overflow: hidden;
  border-bottom-left-radius: 50px;
  border-bottom-right-radius: 50px;
  margin-bottom: 30px;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://images.unsplash.com/photo-1606811971618-4486d14f3f99?auto=format&fit=crop&q=80') right center no-repeat;
  background-size: contain;
  opacity: 0.05;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

/* Cards and Boxes */
.card {
  border: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
  overflow: hidden;
  background: #ffffff;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-color);
}

.feature-box, 
.product-card,
.department-box, 
.pricing-box, 
.testimonial-box, 
.doctor-box {
  background: #fff;
  border-radius: var(--border-radius);
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  padding: 2rem;
  height: 100%;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(230, 230, 230, 0.5);
}

.feature-box::after,
.department-box::after,
.pricing-box::after,
.testimonial-box::after,
.doctor-box::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background: linear-gradient(to bottom, var(--primary-light) 0%, rgba(255,255,255,0) 100%);
  z-index: -1;
  transition: height 0.5s ease;
  opacity: 0.5;
}

.feature-box:hover::after,
.department-box:hover::after,
.pricing-box:hover::after,
.testimonial-box:hover::after,
.doctor-box:hover::after {
  height: 100%;
}

.feature-box:hover, 
.department-box:hover, 
.pricing-box:hover, 
.testimonial-box:hover, 
.doctor-box:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-color);
}

.feature-box img,
.product-icon img {
  width: 64px;
  height: 64px;
  transition: var(--transition);
  filter: drop-shadow(0 5px 15px rgba(38, 224, 127, 0.2));
}

.feature-box:hover img,
.product-card:hover .product-icon img {
  transform: scale(1.1) rotate(5deg);
}

.stat-box {
  position: relative;
  transition: var(--transition);
  padding: 1.5rem;
  border-radius: var(--border-radius-sm);
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-box:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-sm);
}

.stat-box h4 {
  color: var(--primary);
  font-weight: 800;
  font-size: 2.2rem;
  margin-bottom: 5px;
}

.product-image {
  height: 220px;
  object-fit: cover;
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
  transition: transform 0.5s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

/* Badge */
.badge-new {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(45deg, #ff6b6b, #ff9f43);
  color: white;
  padding: 7px 15px;
  border-radius: 30px;
  font-size: 0.85rem;
  z-index: 1;
  font-weight: 700;
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

/* Accordion */
.accordion-item {
  border: none;
  margin-bottom: 15px;
  border-radius: var(--border-radius-sm) !important;
  overflow: hidden;
  background: #ffffff;
  box-shadow: var(--shadow-sm);
}

.accordion-button {
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  border-radius: var(--border-radius-sm) !important;
}

.accordion-button:not(.collapsed) {
  color: var(--primary);
  background: linear-gradient(to right, var(--accent), #ffffff);
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: 0 0 0 4px rgba(38, 224, 127, 0.1);
  border-color: var(--primary);
}

.accordion-body {
  padding: 1.5rem;
  line-height: 1.7;
}

/* Special Elements */
.contact-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent), rgba(255,255,255,0.8));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  transition: var(--transition);
  box-shadow: 0 10px 25px rgba(38, 224, 127, 0.15);
}

.contact-info-card:hover .contact-icon {
  transform: translateY(-10px) scale(1.1);
  box-shadow: 0 15px 35px rgba(38, 224, 127, 0.2);
}

.contact-icon img {
  width: 40px;
  height: 40px;
  transition: var(--transition);
}

.contact-info-card:hover .contact-icon img {
  transform: scale(1.2);
}

.working-hours {
  background: linear-gradient(135deg, #eafafc 0%, #ffffff 100%);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: var(--shadow-md);
}

.map-container {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 5px solid #ffffff;
}

.blob-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 500px;
  height: 500px;
  background: radial-gradient(circle, rgba(38, 224, 127, 0.1) 0%, rgba(255,255,255,0) 70%);
  border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
  animation: blob-animation 8s infinite linear;
  opacity: 0.7;
  z-index: -1;
}

@keyframes blob-animation {
  0% { border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%; }
  25% { border-radius: 61% 39% 33% 67% / 69% 56% 44% 31%; }
  50% { border-radius: 37% 63% 56% 44% / 34% 53% 47% 66%; }
  75% { border-radius: 55% 45% 33% 67% / 63% 30% 70% 37%; }
  100% { border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%; }
}

/* New 2025 Elements */
.curved-section {
  position: relative;
  padding: 100px 0;
}

.curved-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: #ffffff;
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
}

.curved-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background: #ffffff;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
}

/* Custom form checkbox and radio */
.form-check-input:focus {
  border-color: var(--primary) !important;
  box-shadow: 0 0 0 0.25rem var(--primary-light) !important;
}

/* Fix badge colors */
.badge {
  padding: 0.5em 0.8em;
}

/* Override table primary color */
.table-primary {
  --bs-table-bg: var(--primary-light) !important;
  --bs-table-striped-bg: rgba(38, 224, 127, 0.1) !important;
  --bs-table-active-bg: rgba(38, 224, 127, 0.2) !important;
  --bs-table-hover-bg: rgba(38, 224, 127, 0.08) !important;
  color: #212529 !important;
  border-color: var(--primary) !important;
}

/* Progress bar */
.progress-bar {
  background-color: var(--primary) !important;
}

/* Override alert primary colors */
.alert-primary {
  background-color: var(--primary-light) !important;
  border-color: var(--primary) !important;
  color: #0f5e34 !important;
}

/* List group item primary */
.list-group-item-primary {
  background-color: var(--primary-light) !important;
  color: #0f5e34 !important;
}

.list-group-item.active {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.page-link {
  color: var(--primary) !important;
}

.page-item.active .page-link {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
  color: white !important;
}

.dropdown-item.active, 
.dropdown-item:active {
  background-color: var(--primary) !important;
}

a {
  color: var(--primary);
}

a:hover {
  color: #1fba68;
}

/* Modern Testimonials Section 2025 */
.testimonials-section {
  position: relative;
  background: linear-gradient(135deg, #f9f9f9 0%, #f0f9ff 100%);
  padding: 100px 0;
  overflow: hidden;
}

.testimonials-wrapper {
  position: relative;
  z-index: 2;
}

.testimonial-blob {
  position: absolute;
  top: -100px;
  right: -150px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(38, 224, 127, 0.05) 0%, rgba(255,255,255,0) 70%);
  border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
  animation: blob-animation 15s infinite linear;
  z-index: 1;
}

.testimonial-blob-2 {
  position: absolute;
  bottom: -150px;
  left: -100px;
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, rgba(19, 197, 193, 0.05) 0%, rgba(255,255,255,0) 70%);
  border-radius: 42% 58% 65% 35% / 45% 33% 67% 55%;
  animation: blob-animation 10s infinite linear reverse;
  z-index: 1;
}

.testimonial-card {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 40px 30px;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.testimonial-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 25px 50px rgba(38, 224, 127, 0.15);
  border-color: rgba(38, 224, 127, 0.3);
}

.testimonial-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiLz48L2c+PC9zdmc+');
  z-index: -1;
  opacity: 0.3;
  transition: opacity 0.5s ease;
}

.testimonial-card:hover .testimonial-pattern {
  opacity: 0.5;
}

.testimonial-quote {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, rgba(38, 224, 127, 0.1) 0%, rgba(19, 197, 193, 0.1) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.testimonial-quote i {
  font-size: 20px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.testimonial-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a4a4a;
  margin-bottom: 20px;
  flex-grow: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid rgba(38, 224, 127, 0.1);
  padding-top: 20px;
  position: relative;
}

.author-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid white;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  position: relative;
}

.author-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
  background: linear-gradient(135deg, var(--primary) 0%, #13c5c1 100%) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

.author-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.author-info {
  flex-grow: 1;
  padding-right: 15px;
}

.author-info h5 {
  margin-bottom: 5px;
  color: #333;
  font-weight: 700;
  font-size: 1.1rem;
}

.author-info span {
  color: #777;
  font-size: 0.9rem;
}

.testimonial-rating {
  display: flex;
  gap: 3px;
  margin-top: 10px;
}

.testimonial-rating i {
  color: #ffba08;
}

/* Modern CTA Section 2025 */
.cta-section {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #f9f9f9 100%);
}

.cta-card {
  background: rgba(255, 255, 255, 0.97);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 30px;
  padding: 60px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.8);
  z-index: 1;
}

.cta-bg-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.cta-circle {
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(38, 224, 127, 0.05) 0%, rgba(19, 197, 193, 0.05) 100%);
  top: -150px;
  left: -150px;
  z-index: -1;
}

.cta-circle-2 {
  width: 200px;
  height: 200px;
  bottom: -100px;
  right: -100px;
  top: auto;
  left: auto;
}

.cta-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMjZlMDdmIiBmaWxsLW9wYWNpdHk9IjAuMDUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+PGNpcmNsZSBjeD0iMSIgY3k9IjEiIHI9IjEiLz48L2c+PC9zdmc+');
  z-index: -1;
  opacity: 0.8;
}

.cta-content {
  position: relative;
  z-index: 2;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 20px;
  background: linear-gradient(to right, #333333, #555555);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.cta-text {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 20px;
}

.cta-text .highlight {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.5rem;
}

.discount-badge {
  display: inline-block;
  background: linear-gradient(135deg, var(--primary) 0%, #13c5c1 100%);
  color: white;
  padding: 12px 25px;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 10px 25px rgba(38, 224, 127, 0.25);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.discount-badge::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  transform: rotate(45deg);
  z-index: -1;
  transition: all 0.5s ease;
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    left: -50%;
  }
  100% {
    left: 150%;
  }
}

.cta-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(135deg, var(--primary) 0%, #13c5c1 100%);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  font-weight: 700;
  font-size: 1.1rem;
  text-decoration: none;
  box-shadow: 0 15px 30px rgba(38, 224, 127, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.cta-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.cta-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 20px 40px rgba(38, 224, 127, 0.3);
  color: white;
}

.cta-btn:hover::before {
  opacity: 1;
}

.cta-btn i {
  transition: transform 0.3s ease;
}

.cta-btn:hover i {
  transform: translateX(-5px);
}

@media (max-width: 991px) {
  .cta-card {
    padding: 40px 30px;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .discount-badge {
    font-size: 1rem;
    padding: 10px 20px;
  }
}

@media (max-width: 767px) {
  .testimonial-card, .cta-card {
    padding: 30px 20px;
  }
  
  .testimonial-author {
    flex-wrap: wrap;
  }
  
  .testimonial-rating {
    width: 100%;
    justify-content: flex-end;
    margin-top: 10px;
  }
}

/* Modern Navbar 2025 */
.navbar-modern {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  padding: 0;
  height: 85px;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.05);
}

.navbar-modern.scrolled {
  height: 70px;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(235, 235, 235, 0.9);
  box-shadow: 0 8px 35px rgba(0, 0, 0, 0.08);
}

.navbar-modern .container {
  height: 100%;
  max-width: 1300px;
}

/* Brand */
.brand-modern {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  transition: all 0.4s ease;
}

.brand-icon-modern {
  width: 48px;
  height: 48px;
  background: var(--primary-gradient);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 10px 25px rgba(38, 224, 127, 0.25);
  transform-style: preserve-3d;
  perspective: 800px;
}

.brand-icon-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 60%);
  z-index: 1;
}

.brand-icon-modern::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.3), rgba(255,255,255,0));
  transform: translateX(-100%);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  20% { transform: translateX(100%); }
  100% { transform: translateX(100%); }
}

.brand-icon-modern img {
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
  position: relative;
  z-index: 2;
  transition: all 0.4s ease;
}

.brand-text-modern {
  font-size: 1.6rem;
  font-weight: 800;
  background: linear-gradient(to right, var(--primary), #13c5c1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  letter-spacing: -0.5px;
  transition: all 0.4s ease;
  position: relative;
}

.brand-text-modern::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--primary-gradient);
  transition: width 0.4s ease;
  border-radius: 5px;
}

.navbar-brand:hover .brand-icon-modern {
  transform: translateY(-5px) rotate3d(1, 1, 1, 10deg);
  box-shadow: 0 15px 30px rgba(38, 224, 127, 0.35);
}

.navbar-brand:hover .brand-icon-modern img {
  transform: scale(1.15);
}

.navbar-brand:hover .brand-text-modern::after {
  width: 100%;
}

/* Nav Menu */
.nav-menu-modern {
  display: flex;
  align-items: center;
  height: 100%;
  margin: 0;
  padding: 0;
  gap: 5px;
}

.nav-item-modern {
  height: 100%;
  display: flex;
  align-items: center;
  position: relative;
}

.nav-link-modern {
  position: relative;
  font-weight: 700;
  font-size: 1.05rem;
  color: #333;
  padding: 0.5rem 1.2rem !important;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 100%;
}

.nav-link-modern::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 3px;
  background: var(--primary-gradient);
  transition: width 0.4s cubic-bezier(0.65, 0, 0.35, 1);
  border-radius: 3px 3px 0 0;
}

.nav-link-modern::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(38, 224, 127, 0.08);
  border-radius: 10px;
  opacity: 0;
  transform: scale(0.85);
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: -1;
}

.nav-link-modern i {
  font-size: 0.9rem;
  opacity: 0.7;
  transition: all 0.4s ease;
}

.nav-link-modern:hover,
.nav-link-modern.active {
  color: var(--primary);
  transform: translateY(-2px);
}

.nav-link-modern:hover::before,
.nav-link-modern.active::before {
  width: 60%;
}

.nav-link-modern:hover::after,
.nav-link-modern.active::after {
  opacity: 1;
  transform: scale(1);
}

.nav-link-modern:hover i,
.nav-link-modern.active i {
  transform: translateY(-2px);
  opacity: 1;
  color: var(--primary);
}

/* CTA Button */
.navbar-cta-modern {
  margin-right: 15px;
}

.btn-navbar-cta-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: var(--primary-gradient);
  color: white;
  font-weight: 700;
  padding: 12px 24px;
  border-radius: 50px;
  text-decoration: none;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 8px 25px rgba(38, 224, 127, 0.25);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
  perspective: 800px;
}

.btn-navbar-cta-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 60%);
  z-index: 1;
}

.btn-navbar-cta-modern::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transform: rotate(30deg);
  transition: all 1s ease;
  animation: shine 3s infinite;
}

.btn-navbar-cta-modern .btn-text {
  position: relative;
  z-index: 2;
  transition: all 0.4s ease;
}

.btn-navbar-cta-modern .btn-icon {
  position: relative;
  z-index: 2;
  transition: all 0.4s ease;
  font-size: 0.9rem;
  opacity: 0.9;
}

.btn-navbar-cta-modern:hover {
  transform: translateY(-5px) rotateX(10deg);
  box-shadow: 0 15px 35px rgba(38, 224, 127, 0.35);
  color: white;
}

.btn-navbar-cta-modern:hover .btn-icon {
  transform: translateX(-5px);
}

/* Mobile Toggle */
.navbar-toggler-modern {
  border: none;
  background: transparent;
  padding: 0;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 100;
}

.navbar-toggler-modern:focus {
  box-shadow: none;
}

.navbar-toggler-modern span {
  position: relative;
}

.toggler-icon {
  width: 25px;
  height: 2px;
  background: var(--primary);
  display: block;
  transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
  position: relative;
}

.toggler-icon::before,
.toggler-icon::after {
  content: '';
  position: absolute;
  width: 25px;
  height: 2px;
  background: var(--primary);
  display: block;
  transition: all 0.3s cubic-bezier(0.65, 0, 0.35, 1);
}

.toggler-icon::before {
  top: -8px;
  right: 0;
  width: 20px;
}

.toggler-icon::after {
  bottom: -8px;
  right: 0;
  width: 15px;
}

.navbar-toggler-modern[aria-expanded="true"] .toggler-icon {
  background: transparent;
}

.navbar-toggler-modern[aria-expanded="true"] .toggler-icon::before {
  top: 0;
  transform: rotate(45deg);
  width: 25px;
}

.navbar-toggler-modern[aria-expanded="true"] .toggler-icon::after {
  bottom: 0;
  transform: rotate(-45deg);
  width: 25px;
}

/* Responsive Mobile Menu */
@media (max-width: 991px) {
  .navbar-modern {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 15px 0;
    height: auto;
  }
  
  .navbar-collapse-modern {
    position: fixed;
    top: 0;
    right: -100%;
    width: 300px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: -5px 0 30px rgba(0, 0, 0, 0.1);
    z-index: 99;
    padding: 100px 25px 30px;
    transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
    overflow-y: auto;
  }
  
  .navbar-collapse-modern.show {
    right: 0;
  }
  
  .nav-menu-modern {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    height: auto;
  }
  
  .nav-item-modern {
    width: 100%;
    height: auto;
  }
  
  .nav-link-modern {
    width: 100%;
    padding: 12px 20px !important;
    border-radius: 12px;
    justify-content: space-between;
  }
  
  .nav-link-modern::before {
    display: none;
  }
  
  .nav-link-modern i {
    transform: rotate(-90deg);
  }
  
  .nav-link-modern.active {
    background: rgba(38, 224, 127, 0.1);
  }
  
  .navbar-cta-modern {
    margin: 20px 0 0;
    width: 100%;
  }
  
  .btn-navbar-cta-modern {
    width: 100%;
    justify-content: center;
  }
  
  .overlay-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 98;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s ease;
  }
  
  .overlay-bg.active {
    opacity: 1;
    visibility: visible;
  }
}

/* 2025 Footer Styles */
.footer-2025 {
  position: relative;
  background: var(--footer-bg);
  color: var(--footer-text);
  overflow: hidden;
  margin-top: 80px;
}

.footer-waves {
  position: absolute;
  top: -120px;
  left: 0;
  width: 100%;
  line-height: 0;
  overflow: hidden;
}

.footer-waves svg {
  width: 100%;
  height: 120px;
}

.footer-content {
  position: relative;
  z-index: 1;
  padding: 80px 0 40px;
}

.footer-brand {
  margin-bottom: 30px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.footer-logo-icon {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(38, 224, 127, 0.25);
}

.footer-logo-icon img {
  width: 30px;
  height: 30px;
}

.footer-logo-text {
  font-size: 1.4rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.footer-description {
  margin-bottom: 25px;
  line-height: 1.7;
  font-size: 0.95rem;
}

.footer-social {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.social-link:hover {
  transform: translateY(-5px);
  color: white;
}

.social-link:hover::before {
  opacity: 1;
}

.footer-heading {
  color: var(--footer-heading);
  font-weight: 700;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 10px;
  display: inline-block;
}

.footer-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--primary-gradient);
  border-radius: 3px;
}

.footer-links ul {
  margin: 0;
  padding: 0;
}

.footer-links ul li {
  margin-bottom: 12px;
}

.footer-links ul li a {
  color: var(--footer-link);
  text-decoration: none;
  transition: all 0.3s ease;
  display: block;
  padding: 5px 0;
  font-weight: 500;
}

.footer-links ul li a:hover {
  color: var(--footer-link-hover);
  transform: translateX(8px);
}

.footer-contact {
  margin-bottom: 30px;
}

.contact-info {
  margin-bottom: 25px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
}

.contact-text p {
  margin: 0;
  font-size: 0.95rem;
}

.newsletter-form {
  margin-top: 25px;
}

.form-container {
  display: flex;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  padding: 5px;
}

.form-container input {
  border: none;
  background: transparent;
  padding: 15px 20px;
  color: white;
  flex: 1;
  outline: none;
  font-size: 0.95rem;
}

.form-container input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-container button {
  background: var(--primary-gradient);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-container button:hover {
  transform: scale(1.05);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 25px 0;
  text-align: center;
}

.copyright p {
  margin: 0;
  font-size: 0.9rem;
}

@media (max-width: 991px) {
  .footer-logo {
    justify-content: center;
  }
  
  .footer-description,
  .footer-heading {
    text-align: center;
  }
  
  .footer-heading::after {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .footer-social {
    justify-content: center;
  }
  
  .contact-item {
    justify-content: center;
  }
  
  .footer-links ul li a {
    text-align: center;
  }
  
  .footer-links ul li a:hover {
    transform: none;
  }
}

/* Add custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #26e07f, #13c5c1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #20c76f, #11b0ad);
}

/* تحسينات إضافية للنافبار */
.navbar-simple {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.98);
  height: 80px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  padding: 0;
  transition: all 0.3s ease;
}

.navbar-simple.scrolled {
  height: 70px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
}

.navbar-simple .container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  max-width: 1300px;
  padding: 0 20px;
  position: relative;
}

.navbar-brand-simple {
  position: absolute;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
}

.brand-logo {
  width: 45px;
  height: 45px;
  background: #26e07f;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(38, 224, 127, 0.15);
  transition: all 0.3s ease;
}

.brand-logo:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(38, 224, 127, 0.2);
}

.brand-logo img {
  width: 25px;
  height: 25px;
  filter: brightness(0) invert(1);
}

.brand-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #26e07f;
  transition: all 0.3s ease;
}

.navbar-brand-simple:hover .brand-title {
  transform: translateY(-1px);
}

.nav-list {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  list-style: none;
  gap: 25px;
}

.nav-item-simple {
  position: relative;
  margin: 0 5px;
}

.nav-link-simple {
  display: flex;
  align-items: center;
  padding: 8px 18px;
  color: #333;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.05rem;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.nav-link-simple.active {
  color: #26e07f;
  background: rgba(38, 224, 127, 0.08);
}

.nav-link-simple:hover {
  color: #26e07f;
  transform: translateY(-2px);
}

.cta-button {
  position: absolute;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: #26e07f;
  color: white;
  padding: 10px 22px;
  border-radius: 30px;
  font-weight: 700;
  font-size: 1rem;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(38, 224, 127, 0.25);
  transition: all 0.3s ease;
  margin-right: 0;
}

.cta-button i {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 18px rgba(38, 224, 127, 0.35);
  color: white;
}

.cta-button:hover i {
  transform: translateX(-3px);
}

/* تعديل بيانات زر اطلب الآن في القائمة */
.nav-item-simple:last-child {
  display: none;
}

@media (max-width: 991px) {
  .navbar-simple .container {
    justify-content: space-between;
  }
  
  .navbar-brand-simple {
    position: relative;
    right: 0;
  }
  
  .cta-button {
    display: none;
  }
  
  .nav-item-simple:last-child {
    display: block;
  }
  
  .nav-list .cta-button {
    position: relative;
    left: 0;
    margin: 10px 0 0;
    width: 100%;
    justify-content: center;
    display: flex;
  }
}

/* قم بإزالة النمط القديم لتجنب التداخل */
.navbar-modern {
  display: none;
}

.mobile-toggle {
  display: none;
  border: none;
  background: transparent;
  font-size: 1.5rem;
  color: #26e07f;
  cursor: pointer;
  padding: 5px;
}

/* تعديل بيانات زر اطلب الآن في القائمة */
.nav-item-simple:last-child {
  display: none;
}

@media (max-width: 991px) {
  .navbar-simple .container {
    justify-content: space-between;
  }
  
  .navbar-brand-simple {
    position: relative;
    right: 0;
  }
  
  .mobile-toggle {
    display: block;
  }
  
  .cta-button {
    display: none;
  }
  
  .nav-item-simple:last-child {
    display: block;
  }
  
  .nav-list {
    position: fixed;
    top: 80px;
    right: -100%;
    width: 250px;
    height: calc(100vh - 80px);
    flex-direction: column;
    background: white;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.08);
    padding: 20px;
    transition: all 0.3s ease;
    align-items: flex-start;
    gap: 15px;
    z-index: 99;
  }
  
  .nav-list.show {
    right: 0;
  }
  
  .nav-item-simple {
    width: 100%;
    margin: 3px 0;
  }
  
  .nav-link-simple {
    width: 100%;
    justify-content: flex-start;
    padding: 12px 15px;
  }
  
  .nav-list .cta-button {
    position: relative;
    left: 0;
    margin: 10px 0 0;
    width: 100%;
    justify-content: center;
    display: flex;
  }
}